#!/bin/bash

# Complete script to migrate from pnpm/npm to yarn
# This script handles the parent directory configuration issue

echo "=== Starting Migration to Yarn ==="

# Step 1: Clean up existing lock files
echo "Removing existing lock files..."
find . -name "package-lock.json" -type f -delete
find . -name "pnpm-lock.yaml" -type f -delete
find . -name "yarn.lock" -type f -delete

# Step 2: Add .npmrc files with yarn configuration
echo "Creating .npmrc files with yarn configuration..."
cat > ./.npmrc << EOF
engine-strict=false
ignore-workspace-root-check=true
use-yarn=true
legacy-peer-deps=true
EOF

cp ./.npmrc ./client/.npmrc
cp ./.npmrc ./server/.npmrc

# Step 3: Create a temporary package.json to run yarn
echo "Temporarily bypassing parent directory configuration..."

# Root directory
echo "Setting up root directory..."
cd /Users/<USER>/Documents/GitHub/shadcn-template
YARN_IGNORE_PARENT=1 NODE_OPTIONS="--no-node-snapshot" yarn install --ignore-engines || echo "Root installation can be skipped. Continuing..."

# Client directory
echo "Setting up client directory..."
cd /Users/<USER>/Documents/GitHub/shadcn-template/client
YARN_IGNORE_PARENT=1 NODE_OPTIONS="--no-node-snapshot" yarn install --ignore-engines || echo "Client installation will need to be done manually."

# Server directory
echo "Setting up server directory..."
cd /Users/<USER>/Documents/GitHub/shadcn-template/server
YARN_IGNORE_PARENT=1 NODE_OPTIONS="--no-node-snapshot" yarn install --ignore-engines || echo "Server installation will need to be done manually."

cd /Users/<USER>/Documents/GitHub/shadcn-template

echo "=== Migration Complete ==="
echo ""
echo "If you encounter issues installing dependencies, try running these commands manually:"
echo ""
echo "cd /Users/<USER>/Documents/GitHub/shadcn-template"
echo "YARN_IGNORE_PARENT=1 NODE_OPTIONS=\"--no-node-snapshot\" yarn install --ignore-engines"
echo ""
echo "cd /Users/<USER>/Documents/GitHub/shadcn-template/client"
echo "YARN_IGNORE_PARENT=1 NODE_OPTIONS=\"--no-node-snapshot\" yarn install --ignore-engines"
echo ""
echo "cd /Users/<USER>/Documents/GitHub/shadcn-template/server"
echo "YARN_IGNORE_PARENT=1 NODE_OPTIONS=\"--no-node-snapshot\" yarn install --ignore-engines"
echo ""
echo "Remember to use 'yarn' instead of 'npm run' or 'pnpm' for all your commands going forward."
