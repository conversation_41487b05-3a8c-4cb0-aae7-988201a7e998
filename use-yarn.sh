#!/bin/bash

# This script forces the use of yarn regardless of parent directory settings

# Clean up any existing lock files
echo "Removing lock files..."
find . -name "package-lock.json" -type f -delete
find . -name "pnpm-lock.yaml" -type f -delete

# Force use yarn in each directory
echo "Setting up root directory..."
export YARN_ENABLE_IMMUTABLE_INSTALLS=false
export npm_config_userconfig="./.npmrc"

# Root directory
cd /Users/<USER>/Documents/GitHub/shadcn-template
NODE_ENV=production npm config set ignore-workspace-root-check true
NODE_ENV=production npm config set use-yarn true
NODE_ENV=production npm config set engine-strict false

# Manually create yarn.lock files
echo "Creating yarn.lock in root directory..."
touch yarn.lock

echo "Creating yarn.lock in client directory..."
cd client
touch yarn.lock
cd ..

echo "Creating yarn.lock in server directory..."
cd server
touch yarn.lock
cd ..

echo "Use the following commands to install dependencies in each directory:"
echo "cd /Users/<USER>/Documents/GitHub/shadcn-template && env -u npm_config_user_agent yarn install --ignore-engines"
echo "cd /Users/<USER>/Documents/GitHub/shadcn-template/client && env -u npm_config_user_agent yarn install --ignore-engines"
echo "cd /Users/<USER>/Documents/GitHub/shadcn-template/server && env -u npm_config_user_agent yarn install --ignore-engines"
