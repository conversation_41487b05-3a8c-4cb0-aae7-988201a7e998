#!/bin/bash
set -e

# This script runs when the PostgreSQL container is initialized
# It creates tables using Prisma schema and populates the database with initial data

echo "🔄 Waiting for PostgreSQL to be fully ready..."
sleep 5

echo "🚀 Starting database initialization..."

# Create a TypeScript script that will be executed to run the Prisma migration
cat > /tmp/init-script.ts << 'EOF'
import { exec } from 'child_process';
import { promisify } from 'util';
import { PrismaClient } from '@prisma/client';
import fs from 'fs';
import path from 'path';

const execAsync = promisify(exec);

async function runCommand(command: string): Promise<void> {
  try {
    const { stdout, stderr } = await execAsync(command);
    console.log(stdout);
    if (stderr) console.error(stderr);
  } catch (error) {
    console.error(`Error executing command: ${command}`);
    console.error(error);
    throw error;
  }
}

async function main() {
  console.log("📝 Running database initialization...");
  
  try {
    // Install necessary packages 
    await runCommand('cd /docker-entrypoint-initdb.d/prisma && npm install @prisma/client');
    
    // Generate Prisma client
    await runCommand('cd /docker-entrypoint-initdb.d/prisma && npx prisma generate');
    
    // Run migrations
    await runCommand('cd /docker-entrypoint-initdb.d/prisma && npx prisma db push --force');
    
    // Run seed script
    await runCommand('cd /docker-entrypoint-initdb.d/prisma && npm install -D ts-node typescript && npx ts-node --transpile-only seed.ts');
    
    console.log("✅ Database initialization completed successfully!");
  } catch (error) {
    console.error("❌ Database initialization failed:", error);
    process.exit(1);
  }
}

main();
EOF

# Run the TypeScript initialization script
echo "🏃 Running database initialization script..."
cd /docker-entrypoint-initdb.d/prisma
npm init -y
npm install -D typescript ts-node @types/node
npm install @prisma/client
npx ts-node /tmp/init-script.ts

echo "✅ Database initialization completed!"
