# Converting to Yarn Package Manager

This guide will help you convert your project from npm/pnpm to yarn.

## Prerequisites

Make sure yarn is installed globally:

```bash
npm install -g yarn
```

## Step-by-Step Instructions

### 1. Root Directory

```bash
cd /Users/<USER>/Documents/GitHub/shadcn-template
rm -f package-lock.json pnpm-lock.yaml
yarn
```

### 2. Client Directory

```bash
cd /Users/<USER>/Documents/GitHub/shadcn-template/client
rm -f package-lock.json pnpm-lock.yaml
yarn
```

### 3. Server Directory

```bash
cd /Users/<USER>/Documents/GitHub/shadcn-template/server
rm -f package-lock.json pnpm-lock.yaml
yarn
```

### 4. Update Scripts (Optional)

If you have any npm or pnpm specific scripts in your package.json files, you can update them to use yarn equivalents:

- `npm run` → `yarn`
- `npm install` → `yarn add`
- `npm install --save-dev` → `yarn add --dev`
- `npx prisma generate` → `yarn prisma generate`

## Verification

After completing the steps above, you should have:

- `yarn.lock` files in each directory
- No `package-lock.json` or `pnpm-lock.yaml` files
- All dependencies installed through yarn

## Troubleshooting

If you encounter issues with dependencies, try the following:

```bash
yarn cache clean
yarn install --force
```

If there's an error about a package being configured to use pnpm, you may need to use the `--ignore-engines` flag:

```bash
yarn install --ignore-engines
```
