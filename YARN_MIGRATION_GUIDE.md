# Converting to Yarn Package Manager

This guide provides step-by-step instructions for converting your project from npm/pnpm to yarn.

## Prerequisites

Ensure yarn is installed globally:

```bash
npm install -g yarn
```

## Step 1: Backup Your package.json Files

Before making any changes, create backups of your package.json files:

```bash
cp /Users/<USER>/Documents/GitHub/shadcn-template/package.json /Users/<USER>/Documents/GitHub/shadcn-template/package.json.bak
cp /Users/<USER>/Documents/GitHub/shadcn-template/client/package.json /Users/<USER>/Documents/GitHub/shadcn-template/client/package.json.bak
cp /Users/<USER>/Documents/GitHub/shadcn-template/server/package.json /Users/<USER>/Documents/GitHub/shadcn-template/server/package.json.bak
```

## Step 2: Remove Lock Files

Remove all existing lock files:

```bash
find /Users/<USER>/Documents/GitHub/shadcn-template -name "package-lock.json" -type f -delete
find /Users/<USER>/Documents/GitHub/shadcn-template -name "pnpm-lock.yaml" -type f -delete
```

## Step 3: Update Scripts in package.json

Modify any npm/pnpm specific scripts in your package.json files to use yarn:

- For the client package.json, update this script:
  - Change `"prisma:seed": "npx ts-node --esm prisma/seed.ts"` to `"prisma:seed": "yarn ts-node --esm prisma/seed.ts"`

- For the server package.json, remove the `"packageManager": "pnpm@8.15.3",` line

## Step 4: Install Dependencies with Yarn

### Root Directory
```bash
cd /Users/<USER>/Documents/GitHub/shadcn-template
NODE_OPTIONS="--no-node-snapshot" yarn install --ignore-engines
```

### Client Directory
```bash
cd /Users/<USER>/Documents/GitHub/shadcn-template/client
NODE_OPTIONS="--no-node-snapshot" yarn install --ignore-engines
```

### Server Directory
```bash
cd /Users/<USER>/Documents/GitHub/shadcn-template/server
NODE_OPTIONS="--no-node-snapshot" yarn install --ignore-engines
```

## Step 5: Verify Installation

After installation, verify that:
- `yarn.lock` files exist in each directory
- No `package-lock.json` or `pnpm-lock.yaml` files remain
- All dependencies are correctly installed

## Alternative Solution

If you're still experiencing issues due to the parent directory configuration, you can temporarily create a standalone project copy:

1. Create a new directory outside of the conflicting parent directory
2. Copy your project there
3. Convert to yarn in that location
4. Copy the resulting yarn.lock files back to your original project

## Running Scripts with Yarn

Update your workflow to use yarn commands:

- `npm run dev` → `yarn dev`
- `npm run build` → `yarn build`
- `npm test` → `yarn test`
- `npx prisma generate` → `yarn prisma generate`
