#!/bin/bash

# Convert project to use yarn

echo "Converting project to use yarn..."

# Remove existing lock files
echo "Removing existing lock files..."
find . -name "package-lock.json" -type f -delete
find . -name "pnpm-lock.yaml" -type f -delete

# Force ignore any parent project settings
export NODE_OPTIONS="--no-node-snapshot"

# Root directory
echo "Installing dependencies in the root directory..."
rm -f yarn.lock
yarn install --ignore-engines

# Client directory
echo "Installing dependencies in the client directory..."
cd client
rm -f yarn.lock
yarn install --ignore-engines
cd ..

# Server directory
echo "Installing dependencies in the server directory..."
cd server
rm -f yarn.lock
yarn install --ignore-engines
cd ..

echo "Conversion complete! The project now uses yarn as the package manager."
