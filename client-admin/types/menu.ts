/**
 * Menu item interface
 */
export interface MenuItem {
  id: string;
  name: string;
  displayName: string;
  path: string;
  icon?: string | null;
  parentId: string | null;
  applicationId: string;
  order: number;
  isVisible: boolean;
  createdAt: string;
  updatedAt: string;
  children?: MenuItem[];
}

/**
 * Menu item form data for create/update
 */
export interface MenuItemFormData {
  name: string;
  displayName: string;
  path: string;
  icon?: string;
  parentId: string | null;
  applicationId: string;
  order?: number;
  isVisible?: boolean;
}

/**
 * Application interface for menu association
 */
export interface Application {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isActive: boolean;
  path: string;
  icon?: string;
  order: number;
}

/**
 * Navigation item interface for client-side use
 */
export interface NavigationItem {
  id: string;
  title: string;
  href: string;
  icon: string | null;
  applicationPath: string;
  parentId: string | null;
  children: NavigationItem[];
  order?: number;
}