/**
 * API Testing Script
 * 
 * This script tests the API endpoints used by the admin panel.
 * Run it with: node scripts/test-api.js
 */

const fetch = require('node-fetch');

// Configuration
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
const AUTH_TOKEN = process.env.TEST_AUTH_TOKEN; // Set this in your environment or .env.local

// Test endpoints
const endpoints = [
  { name: 'Menu Items', path: '/admin/menu', method: 'GET' },
  { name: 'Applications', path: '/admin/applications', method: 'GET' },
  { name: 'Users', path: '/admin/users', method: 'GET' },
  { name: 'Roles', path: '/admin/roles', method: 'GET' },
];

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

/**
 * Test a single API endpoint
 */
async function testEndpoint(endpoint) {
  console.log(`${colors.blue}Testing ${endpoint.name} endpoint: ${endpoint.path}${colors.reset}`);
  
  try {
    const response = await fetch(`${API_URL}${endpoint.path}`, {
      method: endpoint.method,
      headers: {
        'Content-Type': 'application/json',
        ...(AUTH_TOKEN ? { 'Authorization': `Bearer ${AUTH_TOKEN}` } : {})
      }
    });
    
    const status = response.status;
    const isSuccess = status >= 200 && status < 300;
    
    if (isSuccess) {
      console.log(`${colors.green}✓ Status: ${status}${colors.reset}`);
      
      try {
        const data = await response.json();
        console.log(`${colors.green}✓ Response data received${colors.reset}`);
        console.log(`${colors.blue}Data preview:${colors.reset}`, 
          Array.isArray(data) 
            ? `Array with ${data.length} items` 
            : typeof data === 'object' 
              ? Object.keys(data).slice(0, 3).join(', ') + '...'
              : data
        );
      } catch (error) {
        console.log(`${colors.red}✗ Could not parse response as JSON${colors.reset}`);
      }
    } else {
      console.log(`${colors.red}✗ Status: ${status}${colors.reset}`);
      try {
        const errorData = await response.json();
        console.log(`${colors.red}Error:${colors.reset}`, errorData);
      } catch (e) {
        console.log(`${colors.red}Could not parse error response${colors.reset}`);
      }
    }
    
    return isSuccess;
  } catch (error) {
    console.log(`${colors.red}✗ Request failed: ${error.message}${colors.reset}`);
    return false;
  }
}

/**
 * Run all tests
 */
async function runTests() {
  console.log(`${colors.yellow}=== API Endpoint Testing ====${colors.reset}`);
  console.log(`${colors.blue}API URL: ${API_URL}${colors.reset}`);
  console.log(`${colors.blue}Auth Token: ${AUTH_TOKEN ? 'Provided' : 'Not provided'}${colors.reset}`);
  console.log('');
  
  let passed = 0;
  let failed = 0;
  
  for (const endpoint of endpoints) {
    const success = await testEndpoint(endpoint);
    if (success) {
      passed++;
    } else {
      failed++;
    }
    console.log(''); // Add a blank line between tests
  }
  
  console.log(`${colors.yellow}=== Test Results ====${colors.reset}`);
  console.log(`${colors.green}Passed: ${passed}${colors.reset}`);
  console.log(`${colors.red}Failed: ${failed}${colors.reset}`);
  
  if (failed > 0) {
    console.log(`${colors.yellow}Some tests failed. Please check the API endpoints and try again.${colors.reset}`);
    process.exit(1);
  } else {
    console.log(`${colors.green}All tests passed!${colors.reset}`);
    process.exit(0);
  }
}

// Run the tests
runTests().catch(error => {
  console.error(`${colors.red}Test runner error: ${error.message}${colors.reset}`);
  process.exit(1);
});
