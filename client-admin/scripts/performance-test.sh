#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
  echo -e "\n${BLUE}======================================${NC}"
  echo -e "${BLUE}   $1${NC}"
  echo -e "${BLUE}======================================${NC}\n"
}

# Function to handle errors
handle_error() {
  echo -e "${RED}ERROR: $1${NC}"
  if [ "$2" = "exit" ]; then
    exit 1
  fi
}

# Function to run a command and check for errors
run_command() {
  echo -e "${YELLOW}Running: $1${NC}"
  eval $1
  if [ $? -ne 0 ]; then
    handle_error "$1 failed"
    if [ "$2" = "exit" ]; then
      exit 1
    fi
    return 1
  fi
  return 0
}

# Start the testing process
print_header "CLIENT-ADMIN PERFORMANCE TESTING"
echo -e "${YELLOW}This script will run performance tests for the client-admin application.${NC}"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  if [ -d "../client-admin" ]; then
    echo -e "${YELLOW}Changing directory to client-admin...${NC}"
    cd ../client-admin
  elif [ -d "./client-admin" ]; then
    echo -e "${YELLOW}Changing directory to client-admin...${NC}"
    cd ./client-admin
  else
    handle_error "Please run this script from the client-admin directory or its parent directory." "exit"
  fi
fi

# Check for package manager
if [ -f "yarn.lock" ]; then
  package_manager="yarn"
  echo -e "${GREEN}Using Yarn as package manager.${NC}"
else
  package_manager="npm"
  echo -e "${GREEN}Using NPM as package manager.${NC}"
fi

# Check for Lighthouse CI
if ! command -v lhci &> /dev/null; then
  echo -e "${YELLOW}Lighthouse CI not found. Installing...${NC}"
  npm install -g @lhci/cli
fi

# Build the application for production
print_header "BUILDING APPLICATION"
if [ "$package_manager" = "yarn" ]; then
  run_command "yarn build" "exit"
else
  run_command "npm run build" "exit"
fi

# Start the application in production mode
print_header "STARTING APPLICATION"
echo -e "${YELLOW}Starting the application in production mode...${NC}"
if [ "$package_manager" = "yarn" ]; then
  yarn start > /dev/null 2>&1 &
else
  npm run start > /dev/null 2>&1 &
fi
APP_PID=$!

# Wait for the application to start
echo -e "${YELLOW}Waiting for the application to start...${NC}"
sleep 10

# Run Lighthouse CI
print_header "RUNNING LIGHTHOUSE CI"
echo -e "${YELLOW}Running Lighthouse CI...${NC}"

# Create Lighthouse CI configuration if it doesn't exist
if [ ! -f "lighthouserc.js" ]; then
  echo -e "${YELLOW}Creating Lighthouse CI configuration...${NC}"
  cat > lighthouserc.js << EOL
module.exports = {
  ci: {
    collect: {
      url: ['http://localhost:3001/auth/login'],
      numberOfRuns: 3,
    },
    upload: {
      target: 'filesystem',
      outputDir: './lighthouse-results',
    },
    assert: {
      preset: 'lighthouse:recommended',
      assertions: {
        'categories:performance': ['warn', {minScore: 0.8}],
        'categories:accessibility': ['warn', {minScore: 0.9}],
        'categories:best-practices': ['warn', {minScore: 0.9}],
        'categories:seo': ['warn', {minScore: 0.9}],
      },
    },
  },
};
EOL
fi

# Run Lighthouse CI
lhci autorun

# Kill the application
echo -e "${YELLOW}Stopping the application...${NC}"
kill $APP_PID

# Run bundle analysis
print_header "ANALYZING BUNDLE SIZE"
echo -e "${YELLOW}Installing bundle analyzer...${NC}"
if [ "$package_manager" = "yarn" ]; then
  yarn add -D @next/bundle-analyzer
else
  npm install -D @next/bundle-analyzer
fi

# Create bundle analyzer configuration
echo -e "${YELLOW}Creating bundle analyzer configuration...${NC}"
cat > next.config.bundle-analyzer.js << EOL
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: true,
})

const nextConfig = require('./next.config.js')
module.exports = withBundleAnalyzer(nextConfig)
EOL

# Run bundle analysis
echo -e "${YELLOW}Running bundle analysis...${NC}"
if [ "$package_manager" = "yarn" ]; then
  ANALYZE=true NODE_ENV=production yarn build
else
  ANALYZE=true NODE_ENV=production npm run build
fi

print_header "PERFORMANCE TESTING COMPLETED"
echo -e "${GREEN}Performance testing completed!${NC}"
echo -e "${YELLOW}Lighthouse results are available in the lighthouse-results directory.${NC}"
echo -e "${YELLOW}Bundle analysis results have been displayed in your browser.${NC}"
