#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}======================================${NC}"
echo -e "${BLUE}   SETTING UP E2E TESTS WITH PLAYWRIGHT${NC}"
echo -e "${BLUE}======================================${NC}"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  if [ -d "../client-admin" ]; then
    echo -e "${YELLOW}Changing directory to client-admin...${NC}"
    cd ../client-admin
  elif [ -d "./client-admin" ]; then
    echo -e "${YELLOW}Changing directory to client-admin...${NC}"
    cd ./client-admin
  else
    echo -e "${RED}Please run this script from the client-admin directory or its parent directory.${NC}"
    exit 1
  fi
fi

# Check for package manager
if command -v yarn &> /dev/null; then
  package_manager="yarn"
  echo -e "${GREEN}Using Yarn as package manager.${NC}"
else
  package_manager="npm"
  echo -e "${GREEN}Using NPM as package manager.${NC}"
fi

# Install Playwright
echo -e "${YELLOW}Installing Playwright...${NC}"
if [ "$package_manager" = "yarn" ]; then
  yarn add -D @playwright/test
else
  npm install -D @playwright/test
fi

# Install Playwright browsers
echo -e "${YELLOW}Installing Playwright browsers...${NC}"
npx playwright install

# Create Playwright configuration
echo -e "${YELLOW}Creating Playwright configuration...${NC}"
cat > playwright.config.ts << EOL
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e-tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3001',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
  webServer: {
    command: '$package_manager dev',
    url: 'http://localhost:3001',
    reuseExistingServer: !process.env.CI,
  },
});
EOL

# Create E2E test directory
echo -e "${YELLOW}Creating E2E test directory...${NC}"
mkdir -p e2e-tests

# Create sample login test
echo -e "${YELLOW}Creating sample login test...${NC}"
cat > e2e-tests/login.spec.ts << EOL
import { test, expect } from '@playwright/test';

test.describe('Login Page', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/auth/login');
  });

  test('should display login form', async ({ page }) => {
    await expect(page.getByRole('heading', { name: /admin panel login/i })).toBeVisible();
    await expect(page.getByLabel(/email/i)).toBeVisible();
    await expect(page.getByLabel(/password/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /login/i })).toBeVisible();
  });

  test('should show error with invalid credentials', async ({ page }) => {
    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('wrongpassword');
    await page.getByRole('button', { name: /login/i }).click();
    
    // Wait for error message to appear
    await expect(page.getByText(/invalid email or password/i)).toBeVisible({ timeout: 5000 });
  });

  // This test requires a valid user in the database
  test.skip('should login with valid credentials', async ({ page }) => {
    await page.getByLabel(/email/i).fill('<EMAIL>');
    await page.getByLabel(/password/i).fill('password123');
    await page.getByRole('button', { name: /login/i }).click();
    
    // Wait for redirect to admin dashboard
    await expect(page).toHaveURL(/\/admin/, { timeout: 5000 });
  });
});
EOL

# Create sample dashboard test
echo -e "${YELLOW}Creating sample dashboard test...${NC}"
cat > e2e-tests/dashboard.spec.ts << EOL
import { test, expect } from '@playwright/test';

// Helper function to login
async function login(page) {
  await page.goto('/auth/login');
  await page.getByLabel(/email/i).fill('<EMAIL>');
  await page.getByLabel(/password/i).fill('password123');
  await page.getByRole('button', { name: /login/i }).click();
  
  // Wait for redirect to admin dashboard
  await expect(page).toHaveURL(/\/admin/, { timeout: 5000 });
}

test.describe('Admin Dashboard', () => {
  // Skip these tests by default as they require authentication
  test.skip('should display admin dashboard', async ({ page }) => {
    await login(page);
    
    // Check dashboard elements
    await expect(page.getByRole('heading', { name: /admin dashboard/i })).toBeVisible();
    await expect(page.getByText(/overview/i)).toBeVisible();
  });

  test.skip('should navigate to different sections', async ({ page }) => {
    await login(page);
    
    // Navigate to users section
    await page.getByRole('link', { name: /users/i }).click();
    await expect(page).toHaveURL(/\/admin\/users/);
    
    // Navigate to roles section
    await page.getByRole('link', { name: /roles/i }).click();
    await expect(page).toHaveURL(/\/admin\/roles/);
  });
});
EOL

# Update package.json with E2E test scripts
echo -e "${YELLOW}Updating package.json with E2E test scripts...${NC}"
if [ "$package_manager" = "yarn" ]; then
  # Check if jq is installed
  if command -v jq &> /dev/null; then
    jq '.scripts += {"test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}' package.json > package.json.tmp && mv package.json.tmp package.json
    echo -e "${GREEN}Updated package.json with E2E test scripts.${NC}"
  else
    echo -e "${YELLOW}jq is not installed. Please manually add the following scripts to your package.json:${NC}"
    echo -e "${YELLOW}\"test:e2e\": \"playwright test\",${NC}"
    echo -e "${YELLOW}\"test:e2e:ui\": \"playwright test --ui\"${NC}"
  fi
else
  # Check if jq is installed
  if command -v jq &> /dev/null; then
    jq '.scripts += {"test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}' package.json > package.json.tmp && mv package.json.tmp package.json
    echo -e "${GREEN}Updated package.json with E2E test scripts.${NC}"
  else
    echo -e "${YELLOW}jq is not installed. Please manually add the following scripts to your package.json:${NC}"
    echo -e "${YELLOW}\"test:e2e\": \"playwright test\",${NC}"
    echo -e "${YELLOW}\"test:e2e:ui\": \"playwright test --ui\"${NC}"
  fi
fi

echo -e "${GREEN}E2E tests setup complete!${NC}"
echo -e "${YELLOW}To run E2E tests, use:${NC}"
echo -e "${YELLOW}$package_manager test:e2e${NC}"
echo -e "${YELLOW}To run E2E tests with UI, use:${NC}"
echo -e "${YELLOW}$package_manager test:e2e:ui${NC}"
echo -e "${YELLOW}Note: Some tests are skipped by default as they require authentication.${NC}"
echo -e "${YELLOW}Edit the tests in the e2e-tests directory to match your application.${NC}"
