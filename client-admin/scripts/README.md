# Testing Scripts for client-admin

This directory contains various scripts for testing the client-admin application.

## Available Scripts

### `run-tests.sh`

A comprehensive testing script that runs unit tests, linting, and checks dependencies.

```bash
./scripts/run-tests.sh
```

### `test-api.js`

Tests the API endpoints used by the admin panel.

```bash
node scripts/test-api.js
# or
yarn test:api
```

### `setup-e2e-tests.sh`

Sets up end-to-end testing with <PERSON><PERSON>.

```bash
./scripts/setup-e2e-tests.sh
```

### `ci-test.sh`

Runs tests in a CI environment.

```bash
./scripts/ci-test.sh
# or
yarn test:ci
```

### `performance-test.sh`

Runs performance tests using Lighthouse CI and bundle analysis.

```bash
./scripts/performance-test.sh
# or
yarn test:performance
```

### `test-all.sh`

A master script that provides a menu to run all available tests.

```bash
./scripts/test-all.sh
# or
yarn test:all
```

## NPM/Yarn Scripts

The following scripts are available in package.json:

- `test`: Run Jest tests
- `test:watch`: Run Jest tests in watch mode
- `test:coverage`: Run Jest tests with coverage
- `test:api`: Test API endpoints
- `test:e2e`: Run end-to-end tests with Playwright
- `test:e2e:ui`: Run end-to-end tests with Playwright UI
- `test:performance`: Run performance tests
- `test:ci`: Run tests in a CI environment
- `test:all`: Run all available tests
- `test:setup-e2e`: Set up end-to-end testing with Playwright

## Testing Strategy

### Unit Tests

Unit tests are written using Jest and React Testing Library. They test individual components and functions in isolation.

### API Tests

API tests verify that the API endpoints used by the admin panel are working correctly.

### End-to-End Tests

End-to-end tests use Playwright to test the application as a whole, simulating user interactions in a real browser.

### Performance Tests

Performance tests use Lighthouse CI to measure the performance, accessibility, best practices, and SEO of the application. They also include bundle analysis to identify large dependencies.

### CI Tests

CI tests are designed to run in a continuous integration environment. They include all the necessary tests to ensure the application is working correctly before deployment.

## Adding New Tests

### Adding Unit Tests

1. Create a new test file with the `.test.tsx` or `.test.ts` extension
2. Write your tests using Jest and React Testing Library
3. Run the tests with `yarn test`

### Adding API Tests

1. Add new endpoints to test in `scripts/test-api.js`
2. Run the tests with `yarn test:api`

### Adding End-to-End Tests

1. Set up end-to-end testing with `yarn test:setup-e2e`
2. Create a new test file in the `e2e-tests` directory
3. Write your tests using Playwright
4. Run the tests with `yarn test:e2e`

### Adding Performance Tests

1. Add new URLs to test in `lighthouserc.js`
2. Run the tests with `yarn test:performance`
