#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
  echo -e "\n${BLUE}======================================${NC}"
  echo -e "${BLUE}   $1${NC}"
  echo -e "${BLUE}======================================${NC}\n"
}

# Function to handle errors
handle_error() {
  echo -e "${RED}ERROR: $1${NC}"
  if [ "$2" = "exit" ]; then
    exit 1
  fi
}

# Function to run a command and check for errors
run_command() {
  echo -e "${YELLOW}Running: $1${NC}"
  eval $1
  if [ $? -ne 0 ]; then
    handle_error "$1 failed"
    if [ "$2" = "exit" ]; then
      exit 1
    fi
    return 1
  fi
  return 0
}

# Function to ask for confirmation
confirm() {
  echo -e "${YELLOW}$1 (y/n)${NC}"
  read -r response
  if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
    return 0
  else
    return 1
  fi
}

# Start the testing process
print_header "CLIENT-ADMIN TESTING SCRIPT"
echo -e "${YELLOW}This script will run various tests for the client-admin application.${NC}"
echo -e "${YELLOW}Make sure you have all dependencies installed before running this script.${NC}"
echo

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  if [ -d "../client-admin" ]; then
    echo -e "${YELLOW}Changing directory to client-admin...${NC}"
    cd ../client-admin
  elif [ -d "./client-admin" ]; then
    echo -e "${YELLOW}Changing directory to client-admin...${NC}"
    cd ./client-admin
  else
    handle_error "Please run this script from the client-admin directory or its parent directory." "exit"
  fi
fi

# Check for required tools
print_header "CHECKING PREREQUISITES"

# Check for Node.js
if ! command -v node &> /dev/null; then
  handle_error "Node.js is not installed. Please install Node.js before running this script." "exit"
else
  node_version=$(node -v)
  echo -e "${GREEN}Node.js is installed: $node_version${NC}"
fi

# Check for npm/yarn
if command -v yarn &> /dev/null; then
  package_manager="yarn"
  echo -e "${GREEN}Yarn is installed.${NC}"
elif command -v npm &> /dev/null; then
  package_manager="npm"
  echo -e "${GREEN}NPM is installed.${NC}"
else
  handle_error "Neither yarn nor npm is installed. Please install a package manager." "exit"
fi

# Check for environment variables
if [ ! -f ".env.local" ] && [ ! -f ".env" ]; then
  echo -e "${YELLOW}No .env.local or .env file found. Creating a test .env file...${NC}"
  cat > .env.test << EOL
# Test environment variables
NEXTAUTH_URL=http://localhost:3001
NEXTAUTH_SECRET=test-secret
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/test_db?schema=public"
COOKIE_DOMAIN=.example.com
NEXT_PUBLIC_API_URL=http://localhost:3000/api
EOL
  echo -e "${GREEN}Created .env.test file.${NC}"
fi

# Install dependencies if needed
print_header "CHECKING DEPENDENCIES"
if confirm "Do you want to install/update dependencies?"; then
  if [ "$package_manager" = "yarn" ]; then
    run_command "yarn install" "exit"
  else
    run_command "npm install" "exit"
  fi
else
  echo -e "${YELLOW}Skipping dependency installation.${NC}"
fi

# Generate Prisma client if needed
print_header "CHECKING PRISMA CLIENT"
if [ -d "./prisma" ]; then
  if confirm "Do you want to generate the Prisma client?"; then
    run_command "npx prisma generate" "exit"
  else
    echo -e "${YELLOW}Skipping Prisma client generation.${NC}"
  fi
else
  echo -e "${YELLOW}No Prisma directory found. Skipping Prisma client generation.${NC}"
fi

# Run linting
print_header "RUNNING LINTING"
if confirm "Do you want to run linting?"; then
  if [ "$package_manager" = "yarn" ]; then
    run_command "yarn lint"
  else
    run_command "npm run lint"
  fi
else
  echo -e "${YELLOW}Skipping linting.${NC}"
fi

# Run unit tests
print_header "RUNNING UNIT TESTS"
if confirm "Do you want to run unit tests?"; then
  # Set environment variables for testing
  export NODE_ENV=test
  export NEXTAUTH_URL=http://localhost:3001
  export NEXTAUTH_SECRET=test-secret
  export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/test_db?schema=public"
  export COOKIE_DOMAIN=.example.com
  
  if [ "$package_manager" = "yarn" ]; then
    run_command "yarn test"
  else
    run_command "npm test"
  fi
else
  echo -e "${YELLOW}Skipping unit tests.${NC}"
fi

# Run API tests
print_header "RUNNING API TESTS"
if confirm "Do you want to run API tests?"; then
  if [ -f "./scripts/test-api.js" ]; then
    if [ "$package_manager" = "yarn" ]; then
      run_command "yarn test:api"
    else
      run_command "npm run test:api"
    fi
  else
    echo -e "${YELLOW}API test script not found. Skipping API tests.${NC}"
  fi
else
  echo -e "${YELLOW}Skipping API tests.${NC}"
fi

# Run build test
print_header "TESTING BUILD PROCESS"
if confirm "Do you want to test the build process?"; then
  if [ "$package_manager" = "yarn" ]; then
    run_command "yarn build"
  else
    run_command "npm run build"
  fi
else
  echo -e "${YELLOW}Skipping build test.${NC}"
fi

# Start the development server for manual testing
print_header "MANUAL TESTING"
if confirm "Do you want to start the development server for manual testing?"; then
  echo -e "${GREEN}Starting the development server...${NC}"
  echo -e "${GREEN}The application will be available at http://localhost:3001${NC}"
  echo -e "${YELLOW}Press Ctrl+C to stop the server.${NC}"
  
  if [ "$package_manager" = "yarn" ]; then
    yarn dev
  else
    npm run dev
  fi
else
  echo -e "${YELLOW}Skipping manual testing.${NC}"
fi

print_header "TESTING COMPLETED"
echo -e "${GREEN}All requested tests have been completed.${NC}"
echo -e "${YELLOW}If you encountered any issues, please check the error messages above.${NC}"
