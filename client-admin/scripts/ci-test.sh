#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
  echo -e "\n${BLUE}======================================${NC}"
  echo -e "${BLUE}   $1${NC}"
  echo -e "${BLUE}======================================${NC}\n"
}

# Function to handle errors
handle_error() {
  echo -e "${RED}ERROR: $1${NC}"
  exit 1
}

# Function to run a command and check for errors
run_command() {
  echo -e "${YELLOW}Running: $1${NC}"
  eval $1
  if [ $? -ne 0 ]; then
    handle_error "$1 failed"
  fi
}

# Start the testing process
print_header "CLIENT-ADMIN CI TESTING"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  if [ -d "../client-admin" ]; then
    echo -e "${YELLOW}Changing directory to client-admin...${NC}"
    cd ../client-admin
  elif [ -d "./client-admin" ]; then
    echo -e "${YELLOW}Changing directory to client-admin...${NC}"
    cd ./client-admin
  else
    handle_error "Please run this script from the client-admin directory or its parent directory."
  fi
fi

# Check for package manager
if [ -f "yarn.lock" ]; then
  package_manager="yarn"
  echo -e "${GREEN}Using Yarn as package manager.${NC}"
else
  package_manager="npm"
  echo -e "${GREEN}Using NPM as package manager.${NC}"
fi

# Set environment variables for testing
export CI=true
export NODE_ENV=test
export NEXTAUTH_URL=http://localhost:3001
export NEXTAUTH_SECRET=test-secret
export DATABASE_URL="postgresql://postgres:postgres@localhost:5432/test_db?schema=public"
export COOKIE_DOMAIN=.example.com

# Install dependencies
print_header "INSTALLING DEPENDENCIES"
if [ "$package_manager" = "yarn" ]; then
  run_command "yarn install --frozen-lockfile"
else
  run_command "npm ci"
fi

# Generate Prisma client
if [ -d "./prisma" ]; then
  print_header "GENERATING PRISMA CLIENT"
  run_command "npx prisma generate"
fi

# Run linting
print_header "RUNNING LINTING"
if [ "$package_manager" = "yarn" ]; then
  run_command "yarn lint"
else
  run_command "npm run lint"
fi

# Run type checking
print_header "RUNNING TYPE CHECKING"
if [ "$package_manager" = "yarn" ]; then
  run_command "yarn tsc --noEmit"
else
  run_command "npm run tsc -- --noEmit"
fi

# Run unit tests
print_header "RUNNING UNIT TESTS"
if [ "$package_manager" = "yarn" ]; then
  run_command "yarn test --ci --coverage"
else
  run_command "npm test -- --ci --coverage"
fi

# Run build test
print_header "TESTING BUILD PROCESS"
if [ "$package_manager" = "yarn" ]; then
  run_command "yarn build"
else
  run_command "npm run build"
fi

# Run E2E tests if available
if [ -f "playwright.config.ts" ]; then
  print_header "RUNNING E2E TESTS"
  run_command "npx playwright install --with-deps chromium"
  if [ "$package_manager" = "yarn" ]; then
    run_command "yarn test:e2e"
  else
    run_command "npm run test:e2e"
  fi
fi

print_header "ALL TESTS PASSED"
echo -e "${GREEN}All tests have passed successfully!${NC}"
exit 0
