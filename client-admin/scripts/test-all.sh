#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
  echo -e "\n${BLUE}======================================${NC}"
  echo -e "${BLUE}   $1${NC}"
  echo -e "${BLUE}======================================${NC}\n"
}

# Function to handle errors
handle_error() {
  echo -e "${RED}ERROR: $1${NC}"
  if [ "$2" = "exit" ]; then
    exit 1
  fi
}

# Start the testing process
print_header "CLIENT-ADMIN MASTER TEST SCRIPT"
echo -e "${YELLOW}This script will run all available tests for the client-admin application.${NC}"
echo -e "${YELLOW}You can choose which tests to run.${NC}"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
  if [ -d "../client-admin" ]; then
    echo -e "${YELLOW}Changing directory to client-admin...${NC}"
    cd ../client-admin
  elif [ -d "./client-admin" ]; then
    echo -e "${YELLOW}Changing directory to client-admin...${NC}"
    cd ./client-admin
  else
    handle_error "Please run this script from the client-admin directory or its parent directory." "exit"
  fi
fi

# Check for script directory
if [ ! -d "./scripts" ]; then
  handle_error "Scripts directory not found. Please make sure you have the scripts directory in your client-admin directory." "exit"
fi

# Check for test scripts
if [ ! -f "./scripts/run-tests.sh" ]; then
  handle_error "run-tests.sh script not found. Please make sure you have the run-tests.sh script in your scripts directory." "exit"
fi

# Display menu
print_header "TEST SELECTION"
echo -e "1. ${GREEN}Run all tests${NC}"
echo -e "2. ${GREEN}Run unit tests${NC}"
echo -e "3. ${GREEN}Run API tests${NC}"
echo -e "4. ${GREEN}Run E2E tests${NC}"
echo -e "5. ${GREEN}Run performance tests${NC}"
echo -e "6. ${GREEN}Run CI tests${NC}"
echo -e "7. ${GREEN}Setup E2E tests${NC}"
echo -e "8. ${RED}Exit${NC}"
echo
echo -e "${YELLOW}Enter your choice (1-8):${NC}"
read -r choice

case $choice in
  1)
    print_header "RUNNING ALL TESTS"
    echo -e "${YELLOW}This will run all available tests.${NC}"
    echo -e "${YELLOW}Press Enter to continue or Ctrl+C to cancel.${NC}"
    read -r
    
    # Run unit tests
    print_header "RUNNING UNIT TESTS"
    ./scripts/run-tests.sh
    
    # Run API tests
    if [ -f "./scripts/test-api.js" ]; then
      print_header "RUNNING API TESTS"
      node ./scripts/test-api.js
    else
      echo -e "${YELLOW}API test script not found. Skipping API tests.${NC}"
    fi
    
    # Run E2E tests
    if [ -f "playwright.config.ts" ]; then
      print_header "RUNNING E2E TESTS"
      if [ -f "yarn.lock" ]; then
        yarn test:e2e
      else
        npm run test:e2e
      fi
    else
      echo -e "${YELLOW}E2E tests not set up. Skipping E2E tests.${NC}"
      echo -e "${YELLOW}You can set up E2E tests by running option 7.${NC}"
    fi
    
    # Run performance tests
    print_header "RUNNING PERFORMANCE TESTS"
    ./scripts/performance-test.sh
    ;;
  2)
    print_header "RUNNING UNIT TESTS"
    ./scripts/run-tests.sh
    ;;
  3)
    print_header "RUNNING API TESTS"
    if [ -f "./scripts/test-api.js" ]; then
      node ./scripts/test-api.js
    else
      echo -e "${RED}API test script not found.${NC}"
      echo -e "${YELLOW}Please make sure you have the test-api.js script in your scripts directory.${NC}"
    fi
    ;;
  4)
    print_header "RUNNING E2E TESTS"
    if [ -f "playwright.config.ts" ]; then
      if [ -f "yarn.lock" ]; then
        yarn test:e2e
      else
        npm run test:e2e
      fi
    else
      echo -e "${RED}E2E tests not set up.${NC}"
      echo -e "${YELLOW}You can set up E2E tests by running option 7.${NC}"
    fi
    ;;
  5)
    print_header "RUNNING PERFORMANCE TESTS"
    ./scripts/performance-test.sh
    ;;
  6)
    print_header "RUNNING CI TESTS"
    ./scripts/ci-test.sh
    ;;
  7)
    print_header "SETTING UP E2E TESTS"
    ./scripts/setup-e2e-tests.sh
    ;;
  8)
    print_header "EXITING"
    echo -e "${GREEN}Goodbye!${NC}"
    exit 0
    ;;
  *)
    handle_error "Invalid choice. Please enter a number between 1 and 8." "exit"
    ;;
esac

print_header "TESTING COMPLETED"
echo -e "${GREEN}All requested tests have been completed.${NC}"
echo -e "${YELLOW}If you encountered any issues, please check the error messages above.${NC}"
