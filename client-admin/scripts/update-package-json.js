const fs = require('fs');
const path = require('path');

// Get the path to package.json
const packageJsonPath = path.join(__dirname, '..', 'package.json');

// Read the package.json file
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Add test scripts
packageJson.scripts = {
  ...packageJson.scripts,
  "test:api": "node scripts/test-api.js",
  "test:e2e": "playwright test",
  "test:e2e:ui": "playwright test --ui",
  "test:performance": "bash scripts/performance-test.sh",
  "test:ci": "bash scripts/ci-test.sh",
  "test:all": "bash scripts/test-all.sh",
  "test:setup-e2e": "bash scripts/setup-e2e-tests.sh"
};

// Write the updated package.json file
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

console.log('Updated package.json with test scripts.');
