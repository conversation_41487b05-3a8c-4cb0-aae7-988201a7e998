'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import { createLoadingPlaceholder } from "@/lib/microfrontend/remoteLoader";

// This is now using the actual remote module from the sampleApp micro-frontend
// We define a loading component for when the remote module is being fetched
const LoadingPlaceholder = () => (
  <div className="w-full border rounded-md p-4">
    <div className="h-6 bg-gray-200 rounded animate-pulse mb-2 w-1/2"></div>
    <div className="space-y-2">
      <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
      <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
      <div className="h-4 bg-gray-200 rounded animate-pulse w-4/6"></div>
    </div>
    <div className="mt-4 text-sm text-gray-500">Loading Applications Manager...</div>
  </div>
);

// Define types for module federation
declare global {
  interface Window {
    adminApp: {
      get: (module: string) => Promise<any>;
    };
  }
}

// This is a fallback component that will be used when the remote module is not available
const FallbackComponent = ({ applications }: { applications: any[] }) => (
  <div className="space-y-4">
    <div className="p-4 border border-yellow-300 bg-yellow-50 text-yellow-700 rounded">
      <h3 className="font-medium">Remote Module Not Available</h3>
      <p className="text-sm mt-1">Using local fallback component instead. Please ensure the micro-frontend is running at http://localhost:3001.</p>
    </div>
    
    <table className="w-full border-collapse">
      <thead>
        <tr className="bg-gray-100">
          <th className="p-2 text-left border">Name</th>
          <th className="p-2 text-left border">Display Name</th>
          <th className="p-2 text-left border">Description</th>
          <th className="p-2 text-left border">Status</th>
        </tr>
      </thead>
      <tbody>
        {applications.map((app) => (
          <tr key={app.id} className="border-b">
            <td className="p-2 border">{app.name}</td>
            <td className="p-2 border">{app.displayName}</td>
            <td className="p-2 border">{app.description}</td>
            <td className="p-2 border">
              <span className={`px-2 py-1 rounded text-xs ${app.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {app.isActive ? 'Active' : 'Inactive'}
              </span>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
);

// Instead of using dynamic import, we'll create a component that tries to load the remote component
// and falls back to a local implementation if it fails
function ApplicationsManagerWrapper({ applications }: { applications: any[] }) {
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(false);
  const [RemoteComponent, setRemoteComponent] = React.useState<any>(null);

  React.useEffect(() => {
    // Function to load the remote module
    const loadRemoteModule = async () => {
      try {
        // Check if we're in the browser and if the remote module is available
        if (typeof window !== 'undefined' && window.adminApp && window.adminApp.get) {
          console.log('Attempting to load remote module...');
          // Wait a bit to ensure the module is registered
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Try to get the remote module
          const factory = await window.adminApp.get('./ApplicationsManager');
          const Component = factory();
          setRemoteComponent(() => Component);
          setLoading(false);
        } else {
          console.warn('Remote module container not found. Using fallback component.');
          setError(true);
          setLoading(false);
        }
      } catch (err) {
        console.error('Error loading remote module:', err);
        setError(true);
        setLoading(false);
      }
    };

    loadRemoteModule();
  }, []);

  if (loading) {
    return <LoadingPlaceholder />;
  }

  if (error || !RemoteComponent) {
    return <FallbackComponent applications={applications} />;
  }

  return <RemoteComponent applications={applications} />;
}

// We don't need the dynamic import anymore, we're handling it manually
const RemoteApplicationsManager = ApplicationsManagerWrapper;

interface Application {
  id: string;
  name: string;
  displayName: string;
  description: string;
  path: string;
  isActive: boolean;
  roles: string;
  menuItemCount: number;
  order: number;
  createdAt: string;
}

interface ApplicationsManagerClientProps {
  applications: Application[];
}

export function ApplicationsManagerClient({ applications }: ApplicationsManagerClientProps) {
  return (
    <div className="w-full">
      <RemoteApplicationsManager applications={applications} />
    </div>
  );
}