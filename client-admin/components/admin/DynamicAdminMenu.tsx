'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { 
  ChevronDown, 
  ChevronRight, 
  LayoutDashboard,
  Users,
  Shield,
  LayoutGrid,
  Menu,
  Settings,
  HelpCircle,
  FileText
} from 'lucide-react';
import { NavigationItem } from '@/types/menu';
import { fetchMenuItems } from '@/lib/services/clientMenuService';

// Icon mapping
const iconMap: Record<string, React.ReactNode> = {
  LayoutDashboard: <LayoutDashboard className="h-4 w-4" />,
  Users: <Users className="h-4 w-4" />,
  Shield: <Shield className="h-4 w-4" />,
  LayoutGrid: <LayoutGrid className="h-4 w-4" />,
  Menu: <Menu className="h-4 w-4" />,
  Settings: <Settings className="h-4 w-4" />,
  HelpCircle: <HelpCircle className="h-4 w-4" />,
  ShieldCheck: <Shield className="h-4 w-4" />,
  // Add more icons as needed
};

// Default icon if icon name is not found in the map
const defaultIcon = <FileText className="h-4 w-4" />;

// Component for a single menu item
interface MenuItemProps {
  item: NavigationItem;
  level?: number;
  isActive: (href: string) => boolean;
}

function AdminMenuItem({ item, level = 0, isActive }: MenuItemProps) {
  const [expanded, setExpanded] = useState(false);
  const hasChildren = item.children && item.children.length > 0;
  const active = isActive(item.href);
  
  // Auto-expand if a child is active
  const isActiveParent = hasChildren && item.children.some(
    child => isActive(child.href) || child.children?.some(grandchild => isActive(grandchild.href))
  );
  
  // Set initial expanded state based on active status
  useEffect(() => {
    if (isActiveParent || active) {
      setExpanded(true);
    }
  }, [isActiveParent, active]);
  
  // Get the icon component
  const iconComponent = item.icon && iconMap[item.icon] ? iconMap[item.icon] : defaultIcon;
  
  return (
    <div className="py-1">
      <div 
        className={cn(
          "flex items-center justify-between rounded-md",
          active 
            ? "bg-primary text-primary-foreground" 
            : "text-muted-foreground hover:bg-muted hover:text-foreground"
        )}
        style={{ paddingLeft: `${level > 0 ? 8 + level * 12 : 12}px`, paddingRight: "12px" }}
      >
        <Link
          href={item.href}
          className="flex items-center gap-3 py-2 flex-1"
        >
          {iconComponent}
          <span className="text-sm">{item.title}</span>
        </Link>
        
        {hasChildren && (
          <button 
            onClick={() => setExpanded(!expanded)}
            className="p-1 rounded hover:bg-muted"
          >
            {expanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </button>
        )}
      </div>
      
      {hasChildren && expanded && (
        <div className="mt-1">
          {item.children.map(child => (
            <AdminMenuItem 
              key={child.id} 
              item={child} 
              level={level + 1}
              isActive={isActive}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export function DynamicAdminMenu() {
  const [menuItems, setMenuItems] = useState<NavigationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const pathname = usePathname();
  
  // Function to check if a link is active
  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(`${href}/`);
  };
  
  // Fetch menu items when component mounts
  useEffect(() => {
    async function loadMenuItems() {
      setLoading(true);
      try {
        const items = await fetchMenuItems('/admin');
        setMenuItems(items);
      } catch (error) {
        console.error('Failed to load menu items:', error);
      } finally {
        setLoading(false);
      }
    }
    
    loadMenuItems();
  }, []);
  
  if (loading) {
    return <div className="py-4 px-2">Loading menu...</div>;
  }
  
  if (menuItems.length === 0) {
    return (
      <div className="py-4 px-2">
        <div className="py-2">
          <h3 className="mb-2 px-2 text-xs font-semibold text-muted-foreground">
            Dashboard
          </h3>
          <Link
            href="/admin"
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all",
              isActive("/admin")
                ? "bg-primary text-primary-foreground"
                : "text-muted-foreground hover:bg-muted hover:text-foreground"
            )}
          >
            <LayoutDashboard className="h-4 w-4" />
            Overview
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="py-2">
      {menuItems.map(item => (
        <AdminMenuItem 
          key={item.id} 
          item={item} 
          isActive={isActive} 
        />
      ))}
    </div>
  );
}