'use client'

import Link from "next/link";
import { Application } from "@/types/roles";
import { ShieldIcon } from "lucide-react";
import { DynamicAdminMenu } from "./DynamicAdminMenu";

interface AdminSidebarProps {
  applications: Application[];
}

export function AdminSidebar({ applications }: AdminSidebarProps) {
  return (
    <aside className="hidden w-64 border-r bg-background md:block">
      <div className="flex h-16 items-center border-b px-4">
        <Link href="/admin" className="flex items-center gap-2 font-semibold">
          <ShieldIcon className="h-6 w-6" />
          <span>Admin Panel</span>
        </Link>
      </div>
      <div className="p-4">
        <nav className="flex flex-col gap-1">
          {/* Dynamic menu loaded from database */}
          <DynamicAdminMenu />
        </nav>
        
        {/* Applications section - shows available applications */}
        {applications.length > 0 && (
          <div className="py-2 mt-4 border-t pt-4">
            <h3 className="mb-2 px-2 text-xs font-semibold text-muted-foreground">
              Available Applications
            </h3>
            <div className="space-y-1">
              {applications
                .filter(app => app.isActive)
                .map(app => (
                  <Link
                    key={app.id}
                    href={`/${app.path}`}
                    className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm 
                              text-muted-foreground hover:bg-muted hover:text-foreground"
                  >
                    <span className="h-2 w-2 rounded-full bg-green-500"></span>
                    {app.displayName}
                  </Link>
                ))}
            </div>
          </div>
        )}
      </div>
    </aside>
  );
}