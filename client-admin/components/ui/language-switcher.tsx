"use client";

import { useTransition } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import { locales } from '@/i18n';
import { saveLanguagePreference } from '@/lib/language-storage';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Globe } from 'lucide-react';
import { useTranslations } from 'next-intl';

export function LanguageSwitcher() {
  const [isPending, startTransition] = useTransition();
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const t = useTranslations('common');

  // Get the path without the locale prefix
  const getPathWithoutLocale = () => {
    const segments = pathname.split('/');
    // Check if the first segment is a locale
    if (locales.includes(segments[1])) {
      return '/' + segments.slice(2).join('/');
    }
    return pathname;
  };

  // Handle language change
  const handleLanguageChange = (newLocale: string) => {
    if (newLocale === locale) return;

    const pathWithoutLocale = getPathWithoutLocale();

    // Save the language preference before navigation
    saveLanguagePreference(newLocale);

    startTransition(() => {
      // If the new locale is the default locale, we might want to use the path without locale
      // This depends on your localePrefix setting in middleware.ts
      router.push(`/${newLocale}${pathWithoutLocale}`);
    });
  };

  // Get the display name for each locale
  const getLocaleDisplayName = (localeCode: string) => {
    switch (localeCode) {
      case 'en':
        return 'English';
      case 'zh-TW':
        return '繁體中文';
      default:
        return localeCode;
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" disabled={isPending}>
          <Globe className="h-5 w-5" />
          <span className="sr-only">{t('language')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {locales.map((localeOption) => (
          <DropdownMenuItem
            key={localeOption}
            onClick={() => handleLanguageChange(localeOption)}
            className={locale === localeOption ? 'bg-accent font-medium' : ''}
          >
            {getLocaleDisplayName(localeOption)}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
