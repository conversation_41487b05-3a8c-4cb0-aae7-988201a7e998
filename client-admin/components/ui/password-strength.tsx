/**
 * @fileoverview Password strength indicator component
 * @module components/ui/password-strength
 */

import React, { useMemo } from 'react';
import { cn } from '@/lib/utils';

/**
 * Password strength criteria
 */
export const passwordCriteria = {
  minLength: 8,
  hasUppercase: /[A-Z]/,
  hasLowercase: /[a-z]/,
  hasNumber: /[0-9]/,
  hasSpecialChar: /[^A-Za-z0-9]/,
};

/**
 * Password strength levels
 */
export enum PasswordStrength {
  NONE = 0,
  WEAK = 1,
  MEDIUM = 2,
  STRONG = 3,
  VERY_STRONG = 4,
}

/**
 * Calculate password strength based on criteria
 * 
 * @param {string} password - The password to check
 * @returns {PasswordStrength} The password strength level
 */
export function calculatePasswordStrength(password: string): PasswordStrength {
  if (!password) return PasswordStrength.NONE;
  
  let strength = 0;
  
  // Check length
  if (password.length >= passwordCriteria.minLength) {
    strength += 1;
  }
  
  // Check for uppercase letters
  if (passwordCriteria.hasUppercase.test(password)) {
    strength += 1;
  }
  
  // Check for lowercase letters
  if (passwordCriteria.hasLowercase.test(password)) {
    strength += 1;
  }
  
  // Check for numbers
  if (passwordCriteria.hasNumber.test(password)) {
    strength += 1;
  }
  
  // Check for special characters
  if (passwordCriteria.hasSpecialChar.test(password)) {
    strength += 1;
  }
  
  // Map the score to PasswordStrength enum
  if (strength === 0) return PasswordStrength.NONE;
  if (strength <= 2) return PasswordStrength.WEAK;
  if (strength <= 3) return PasswordStrength.MEDIUM;
  if (strength <= 4) return PasswordStrength.STRONG;
  return PasswordStrength.VERY_STRONG;
}

/**
 * Get color and label for password strength
 * 
 * @param {PasswordStrength} strength - The password strength level
 * @returns {{ color: string, label: string }} The color and label for the strength
 */
export function getPasswordStrengthInfo(strength: PasswordStrength): { color: string; label: string } {
  switch (strength) {
    case PasswordStrength.NONE:
      return { color: 'bg-gray-200', label: 'None' };
    case PasswordStrength.WEAK:
      return { color: 'bg-red-500', label: 'Weak' };
    case PasswordStrength.MEDIUM:
      return { color: 'bg-yellow-500', label: 'Medium' };
    case PasswordStrength.STRONG:
      return { color: 'bg-green-500', label: 'Strong' };
    case PasswordStrength.VERY_STRONG:
      return { color: 'bg-green-700', label: 'Very Strong' };
    default:
      return { color: 'bg-gray-200', label: 'None' };
  }
}

interface PasswordStrengthIndicatorProps {
  password: string;
  className?: string;
}

/**
 * Password strength indicator component
 */
export function PasswordStrengthIndicator({ password, className }: PasswordStrengthIndicatorProps) {
  const strength = useMemo(() => calculatePasswordStrength(password), [password]);
  const { color, label } = useMemo(() => getPasswordStrengthInfo(strength), [strength]);
  
  // Calculate the width of the indicator based on strength
  const width = useMemo(() => {
    if (strength === PasswordStrength.NONE) return '0%';
    return `${(strength / 4) * 100}%`;
  }, [strength]);
  
  return (
    <div className={cn('space-y-1', className)}>
      <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
        <div
          className={cn('h-full transition-all duration-300', color)}
          style={{ width }}
        />
      </div>
      <p className="text-xs text-muted-foreground flex justify-between">
        <span>Password strength:</span>
        <span className={cn(
          strength === PasswordStrength.WEAK ? 'text-red-500' : '',
          strength === PasswordStrength.MEDIUM ? 'text-yellow-500' : '',
          strength === PasswordStrength.STRONG ? 'text-green-500' : '',
          strength === PasswordStrength.VERY_STRONG ? 'text-green-700' : '',
        )}>
          {label}
        </span>
      </p>
    </div>
  );
}

interface PasswordRequirementsProps {
  password: string;
  className?: string;
}

/**
 * Password requirements component
 */
export function PasswordRequirements({ password, className }: PasswordRequirementsProps) {
  const requirements = useMemo(() => [
    {
      label: `At least ${passwordCriteria.minLength} characters`,
      met: password.length >= passwordCriteria.minLength,
    },
    {
      label: 'At least one uppercase letter (A-Z)',
      met: passwordCriteria.hasUppercase.test(password),
    },
    {
      label: 'At least one lowercase letter (a-z)',
      met: passwordCriteria.hasLowercase.test(password),
    },
    {
      label: 'At least one number (0-9)',
      met: passwordCriteria.hasNumber.test(password),
    },
    {
      label: 'At least one special character (!@#$%^&*)',
      met: passwordCriteria.hasSpecialChar.test(password),
    },
  ], [password]);
  
  return (
    <div className={cn('space-y-1 text-sm', className)}>
      <p className="font-medium text-xs text-muted-foreground mb-1">Password requirements:</p>
      <ul className="space-y-1">
        {requirements.map((req, index) => (
          <li key={index} className="flex items-center gap-2">
            <span className={cn(
              'h-4 w-4 rounded-full flex items-center justify-center text-xs',
              req.met ? 'bg-green-500 text-white' : 'bg-gray-200',
            )}>
              {req.met ? '✓' : ''}
            </span>
            <span className={cn(
              'text-xs',
              req.met ? 'text-muted-foreground' : 'text-muted-foreground',
            )}>
              {req.label}
            </span>
          </li>
        ))}
      </ul>
    </div>
  );
}
