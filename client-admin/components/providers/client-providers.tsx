'use client';

import { ReactNode } from 'react';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { ToasterProvider } from '@/components/providers/toaster-provider';
import { LanguagePersistence } from '@/components/i18n/language-persistence';

interface ClientProvidersProps {
  children: ReactNode;
}

/**
 * Client-side providers wrapper
 * This component wraps all client-side providers in one place
 */
export function ClientProviders({ children }: ClientProvidersProps) {
  return (
    <LanguagePersistence>
      <ThemeProvider>
        {children}
        <ToasterProvider />
      </ThemeProvider>
    </LanguagePersistence>
  );
}
