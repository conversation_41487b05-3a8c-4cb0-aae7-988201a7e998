"use client";

import { SessionProvider } from "next-auth/react";
import { ReactNode } from "react";

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * AuthProvider component that wraps the application with SessionProvider
 * from NextAuth.js to provide authentication context.
 * 
 * This provider should be used at the root layout level to ensure
 * authentication state is available throughout the application.
 */
export function AuthProvider({ children }: AuthProviderProps) {
  return <SessionProvider>{children}</SessionProvider>;
}