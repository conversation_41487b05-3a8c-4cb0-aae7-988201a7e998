import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";
import { requirePermission } from "@/lib/auth/roleService";
import { permissions } from "server/src/config/permissions";

// GET /api/admin/applications/:id - Get application by ID
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
   try {
    const session = await auth();

    if (!session?.user) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (!await requirePermission(permissions["applications:read"].name)(session.user.id)) {
        console.log(`User ${session.user.id} tried to access application ${id} without applications:read permission`);
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id } = params;

    // Check if user is authenticated and has admin role
    /*if (!session?.user || !session.user.role?.includes("admin")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get application from database
    const application = await db.application.findUnique({
      where: { id }
    });
    
    if (!application) {
      return NextResponse.json(
        { error: "Application not found" },
        { status: 404 }
      );
    }
    
    return NextResponse.json(application);
  } catch (error) {
    console.error("[APPLICATION_GET_BY_ID]", error);
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/admin/applications/:id - Update an application
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (!await requirePermission(permissions["applications:update"].name)(session.user.id)) {
        console.log(`User ${session.user.id} tried to update application ${id} without applications:update permission`);
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id } = params;

    // Parse request body
      const body = await req.json();
      const { name, displayName, description, isActive, path, icon, order } = body;
      
        // Check if name, displayName and path are empty
      if (!name || !displayName || !path) {
        return NextResponse.json(
          { error: "Name, displayName and path are required" },
          { status: 400 }
        );
      }
    
      
    // Update application
    const application = await db.application.update({
      where: { id },
      data: {
        name,
        displayName,
        description,
        isActive,
        path,
        icon,
        order,
      },
    });

    return NextResponse.json(application);
  } catch (error) {
      if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 }
      );
    }
    console.error("[APPLICATION_PUT]", error);
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/applications/:id - Delete an application
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    if (!await requirePermission(permissions["applications:delete"].name)(session.user.id)) {
        console.log(`User ${session.user.id} tried to delete application ${id} without applications:delete permission`);
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id } = params;

    // Delete associated menu items
    await db.menuItem.deleteMany({
        where: { applicationId: id },
    });

    // Delete application
    await db.application.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[APPLICATION_DELETE]", error);
    
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}


