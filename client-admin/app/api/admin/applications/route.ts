import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";
import { requirePermission } from "@/lib/auth/roleService";
import { permissions } from "server/src/config/permissions";

// GET /api/admin/applications - Get all applications
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check for applications:read permission
    if (!await requirePermission(permissions["applications:read"].name)(session.user.id)) {
      console.log(`User ${session.user.id} tried to access applications without applications:read permission`);
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }

    // Get all applications from database
    const applications = await db.application.findMany({
      orderBy: { order: 'asc' }
    });

    // Filter applications based on user's access
    const filteredApplications = applications.filter(app => session.user.applicationPaths.includes(app.path));


    return NextResponse.json(filteredApplications);
  } catch (error) {
    console.error("[APPLICATIONS_GET]", error);
    console.log("An internal server error occured");
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/admin/applications - Create a new application
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check for applications:create permission
    if (!await requirePermission(permissions["applications:create"].name)(session.user.id)) {
      console.log(`User ${session.user.id} tried to create an application without applications:create permission`);
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { name, displayName, description, isActive, path, icon, order } = body;

    //Create the application
    const application = await db.application.create({ data: { name, displayName, description, isActive, path, icon, order } })

    return NextResponse.json(application);
  } catch (error) {
    console.error("[APPLICATIONS_POST]", error);
    console.log("An internal server error occured");
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}