import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";
import { requirePermission } from "@/lib/auth/roleService";
import { permissions } from "server/src/config/permissions";

// GET /api/admin/menu - Get all menu items
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check for menu:read permission
    if (!await requirePermission(permissions["menu:read"].name)(session.user.id)) {
      console.log(
        `User ${session.user.id} tried to access the menu without the menu:read permission.`
      );
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }




    
    // Get query params
    const { searchParams } = new URL(req.url);
    const applicationId = searchParams.get("applicationId");
    const section = searchParams.get("section");
    
    // Build base query
    let query: any = {};
    
    // If applicationId is provided, filter by it
    if (applicationId) {
      query.where = { ...query.where, applicationId };
    }
    
    // If section is provided, filter by path starting with that section
    if (section) {
      query.where = { 
        ...query.where, 
        path: {
          startsWith: section
        } 
      };
    }
    
    // Get menu items from database
    const rawMenuItems = await db.menuItem.findMany({
      ...query,
      where: query.where || undefined,
      orderBy: { order: 'asc' },
      include: {
        permissions: {
              include: {
              permission: true
            }
        },
        applications: {
          include: {
              application: true
          }
        }
      }
    });

        const formattedMenuItems = rawMenuItems.map(menuItem => ({
      ...menuItem,
      permissionNames: menuItem.permissions.map(p => p.permission.name),
      applicationPaths: menuItem.applications.map(a => a.application.path)
    }));

    // Filter menu items based on user's application access
    const filteredMenuItems = formattedMenuItems.filter((menuItem: any) => {
      // If the menu item has no applications, keep it
      if (!menuItem.applicationPaths || menuItem.applicationPaths.length === 0) {
        return true;
      }
      return menuItem.applicationPaths.some((appPath: string) => session.user.applicationPaths.includes(appPath));
    });
    
        
        
        
        
  


    // Return the raw menu items directly - the client will handle tree building
    return NextResponse.json(filteredMenuItems);
  } catch (error) {
    console.error("[MENU_GET]", error);
     console.log("Internal server error while getting menu items");
        return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/admin/menu - Create a new menu item
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check for menu:create permission
    if (!await requirePermission(permissions["menu:create"].name)(session.user.id)) {
         console.log(
        `User ${session.user.id} tried to create a menu item without the menu:create permission.`
      );
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }    
    
    // Parse request body
    const body = await req.json();
    const { name, displayName, path, icon, parentId, applicationId, order, isVisible, permissionNames, applicationPaths } = body;

    if (!name || !displayName || !path || order === undefined || isVisible === undefined) {
      return NextResponse.json(
        { error: "Name, displayName, path, order and isVisible are required" },
        { status: 400 }
      );
    }

    // Create menu item
    const menuItem = await db.menuItem.create({
       data: {
        name,
        displayName,
        path,
        icon,
        parentId,
        applicationId,
        order,
        isVisible,
      },
      include: {
        application: true,
        permissions: {
          include: {
            permission: true
          }
        },
        applications: {
          include: {
            application: true
          }
        }
      }
    });

    const formattedMenuItem = {
      ...menuItem,
      permissionNames: menuItem.permissions.map(p => p.permission.name),
      applicationPaths: menuItem.applications.map(a => a.application.path)
    };
    
    return NextResponse.json(formattedMenuItem);
  } catch (error) {
    console.error("[MENU_POST]", error);
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 }
      );
    } else {
      console.log(
        "Internal server error while creating menu items"
      );
      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      );
    }
  }
}