import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";
import { requirePermission } from "@/lib/auth/roleService";
import { permissions } from "server/src/config/permissions";

// PUT /api/admin/menu/:id - Update a menu item
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    const { id } = params;

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check for menu:update permission
    if (!await requirePermission(permissions["menu:update"].name)(session.user.id)) {
      console.log(`User ${session.user.id} tried to update a menu item without menu:update permission`);
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }
    
     // Parse request body
     const body = await req.json();
     const { name, displayName, path, icon, parentId, applicationId, order, isVisible, permissionNames, applicationPaths } = body;
     
      // Check if name, displayName, path, order and isVisible are defined
     if (name === undefined || displayName === undefined || path === undefined || order === undefined || isVisible === undefined) {
       return NextResponse.json(
         { error: "Name, displayName, path, order and isVisible are required" },
         { status: 400 }
       );
     }
    

    
    // Get all the permissions from the database
    const permissions = permissionNames ? await db.permission.findMany({
      where: { name: { in: permissionNames } }
    }) : [];
    
    // Get all the applications from the database
    const applications = applicationPaths ? await db.application.findMany({
      where: { path: { in: applicationPaths } }
    }) : [];

    // Update menu item and the relation with the permissions and applications
    const menuItem = await db.menuItem.update({
      where: { id },
      data: {
        name: name !== undefined ? name : undefined,
        displayName: displayName !== undefined ? displayName : undefined,
        path: path !== undefined ? path : undefined,
        icon: icon !== undefined ? icon : undefined,
        parentId: parentId !== undefined ? parentId : undefined,
        applicationId: applicationId !== undefined ? applicationId : undefined,
        order: order !== undefined ? order : undefined,
        isVisible: isVisible !== undefined ? isVisible : undefined,
        permissions: {
          deleteMany: {},
          create: permissions.map(permission => ({
            permissionId: permission.id
          }))
        },
        applications: {
          deleteMany: {},
          create: applications.map(application => ({
            applicationId: application.id
          }))
        }
      },
      include: {
        permissions: {
          include: { permission: true }
        },
        applications: {
          include: { application: true }
        }
      }
    });
    
    return NextResponse.json({ ...menuItem, permissionNames: menuItem.permissions.map(p => p.permission.name), applicationPaths: menuItem.applications.map(a => a.application.path)});
  } catch (error) {
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 }
      );
    }
    console.error(`[MENU_PUT] error during the update of a menu item`);
    console.error("[MENU_PUT]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/menu/:id - Delete a menu item
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    const { id } = params;

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check for menu:delete permission
    if (!await requirePermission(permissions["menu:delete"].name)(session.user.id)) {
      console.log(`User ${session.user.id} tried to delete a menu item without menu:delete permission`);
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }
    
    // Delete menu item
    await db.menuItemPermission.deleteMany({
      where: { menuItemId: id }
    });
    await db.menuItemApplication.deleteMany({
      where: { menuItemId: id }
    });
    await db.menuItem.delete({
      where: { id }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`[MENU_DELETE] error during the delete of a menu item`);
    console.error("[MENU_DELETE]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}