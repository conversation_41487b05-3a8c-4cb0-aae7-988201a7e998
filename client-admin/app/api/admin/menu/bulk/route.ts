import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";
import { requirePermission } from "@/lib/auth/roleService";
import { permissions } from "server/src/config/permissions";

// POST /api/admin/menu/bulk - Bulk update menu items (reordering, etc)
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    // Check if user is authenticated
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check for menu:update permission
    if (!await requirePermission(permissions["menu:update"].name)(session.user.id)) {
      console.log(
        `User ${session.user.id} tried to update menu items without menu:update permission`
      );

      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      );
    }
    
    let body;
    try {
      // Parse request body
       body = await req.json();
    } catch (error) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 }
      );
    }
    const { items } = body;

     // Check if name, displayName, path, order and isVisible are defined for each item
    if (items.some((item: any) => item.name === undefined || item.displayName === undefined || item.path === undefined || item.order === undefined || item.isVisible === undefined)) {
      return NextResponse.json(
        { error: "Name, displayName, path, order and isVisible are required for each item" },
        { status: 400 }
      );
    }

    
    // Validate items array
    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: "Items array is required" },
        { status: 400 }
      );
    }
    
    // Perform bulk update
    const updatePromises = items.map(item => {
      const { id, order, parentId, permissionNames, applicationPaths } = item;
      
      if (!id) return null;

      return db.menuItem.findUnique({ where: { id } }).then(async menuItem => {
        if (!menuItem) return null;

        const updateData: any = {};
        if (order !== undefined) updateData.order = order;
        if (parentId !== undefined) updateData.parentId = parentId;
        
        // Handle permissions
        if (permissionNames) {
            // Delete current relations
            await db.menuItemPermission.deleteMany({
              where: { menuItemId: id }
            });

            // Retrieve permissions
            const permissions = await db.permission.findMany({
              where: { name: { in: permissionNames } }
            });
        
            // Create new relations
            await Promise.all(permissions.map(permission =>
              db.menuItemPermission.create({
                data: {
                  menuItemId: id,
                  permissionId: permission.id
                }
              })
            ));
          }

          // Handle applications
          if (applicationPaths) {
            // Delete current relations
            await db.menuItemApplication.deleteMany({
              where: { menuItemId: id }
            });

            // Retrieve applications
            const applications = await db.application.findMany({
              where: { path: { in: applicationPaths } }
            });

            await Promise.all(applications.map(application =>
              db.menuItemApplication.create({
                data: {
                  menuItemId: id,
                  applicationId: application.id
                }
              })
            ));
          }

          return db.menuItem.update({
            where: { id },
            data: updateData
          });
        }
      });
    }).filter(Boolean); // Filter out null promises
    
    await Promise.all(updatePromises);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 }
      );
    }
    console.log(`[MENU_BULK] error: ${error}`);
    console.error("[MENU_BULK]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}