import React from 'react';
import { Metada<PERSON> } from "next";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AdminMenuManager } from "@/components/admin/menu/AdminMenuManager";

export const metadata: Metadata = {
  title: "Menu Management",
  description: "Manage application menu items and navigation",
};

export default function MenuPage() {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Menu Management</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Navigation Menu</CardTitle>
          <CardDescription>
            Manage the application navigation menu structure and permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <AdminMenuManager />
        </CardContent>
      </Card>
    </div>
  );
}