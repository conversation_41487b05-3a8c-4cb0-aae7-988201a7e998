/** @type {import('next').NextConfig} */
const nextConfig = {
  // 允許外部圖片域
  images: {
    domains: ["avatars.githubusercontent.com", "lh3.googleusercontent.com"],
    unoptimized: true
  },
  // 忽略 TypeScript 和 ESLint 錯誤
  typescript: { ignoreBuildErrors: true },
  eslint: { ignoreDuringBuilds: true },
  // 關閉嚴格模式
  reactStrictMode: false,
  // 使用獨立輸出模式
  output: 'standalone',
  // 關閉生產環境中的源碼映射
  productionBrowserSourceMaps: false,
  // 啟用 SWC 編譯器
  swcMinify: true,
  // 啟用 SWC 轉換
  experimental: {
    forceSwcTransforms: true,
    serverComponentsExternalPackages: []
  }
}

module.exports = nextConfig
