import { NavigationItem, MenuItem } from '@/types/menu';

/**
 * Builds a tree structure from menu items
 * @param items Raw menu items from the API
 * @returns Hierarchical navigation items
 */
function buildMenuTree(items: MenuItem[]): NavigationItem[] {
  // Create a map of id to item with empty children array
  const itemMap: Record<string, NavigationItem> = {};
  
  // First map all items to NavigationItem format
  items.forEach(item => {
    itemMap[item.id] = {
      id: item.id,
      title: item.displayName,
      href: item.path,
      icon: item.icon || null,
      applicationPath: '', // Will be filled from application data
      parentId: item.parentId,
      children: [],
      order: item.order
    };
  });
  
  // Then build the tree structure
  const rootItems: NavigationItem[] = [];
  
  items.forEach(item => {
    // Add application path from item if available
    if (item.application) {
      itemMap[item.id].applicationPath = item.application.path;
    }
    
    // If the item has a parentId and the parent exists in our map
    if (item.parentId && itemMap[item.parentId]) {
      // Add this item as a child of its parent
      itemMap[item.parentId].children.push(itemMap[item.id]);
    } else {
      // If no parentId or parent doesn't exist, it's a root item
      rootItems.push(itemMap[item.id]);
    }
  });
  
  // Sort the items
  const sortItems = (items: NavigationItem[]) => {
    items.sort((a, b) => (a.order || 0) - (b.order || 0));
    items.forEach(item => {
      if (item.children.length > 0) {
        sortItems(item.children);
      }
    });
  };
  
  sortItems(rootItems);
  
  return rootItems;
}

/**
 * Fetches menu items for a specific section from the API
 * @param section The section path to get menu items for (e.g., '/admin', '/dashboard')
 * @returns Promise with menu items
 */
export async function fetchMenuItems(section: string): Promise<NavigationItem[]> {
  try {
    const url = `/api/admin/menu${section ? `?section=${encodeURIComponent(section)}` : ''}`;
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch menu items: ${response.statusText}`);
    }
    
    const rawItems = await response.json();
    
    // Transform the raw items into a tree structure
    return buildMenuTree(rawItems);
  } catch (error) {
    console.error('Error fetching menu items:', error);
    return [];
  }
}