import { Application } from '@/types/menu';

/**
 * Fetches all applications from the API
 * @returns Applications
 */
export async function fetchApplications(): Promise<Application[]> {
  const response = await fetch('/api/admin/applications');
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch applications');
  }
  
  return response.json();
}

/**
 * Fetches a single application by ID
 * @param id - Application ID
 * @returns Application
 */
export async function fetchApplicationById(id: string): Promise<Application> {
  const response = await fetch(`/api/admin/applications/${id}`);
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch application');
  }
  
  return response.json();
}