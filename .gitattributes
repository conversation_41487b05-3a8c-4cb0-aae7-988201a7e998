# Next.js build cache files
**/.next/cache/webpack/**/*.pack filter=lfs diff=lfs merge=lfs -text
**/.next-build/cache/webpack/**/*.pack filter=lfs diff=lfs merge=lfs -text

# Node modules binary files
**/node_modules/.pnpm/@next+swc-darwin-arm64*/**/*.node filter=lfs diff=lfs merge=lfs -text
**/node_modules/.pnpm/@prisma+engines*/**/*.node filter=lfs diff=lfs merge=lfs -text
**/node_modules/.pnpm/@prisma+engines*/**/schema-engine-* filter=lfs diff=lfs merge=lfs -text
**/node_modules/.pnpm/@img+sharp-libvips*/**/*.dylib filter=lfs diff=lfs merge=lfs -text

# Large map files
**/node_modules/**/server.runtime.prod.js.map filter=lfs diff=lfs merge=lfs -text
