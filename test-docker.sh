#!/bin/bash

set -e

echo "Starting services..."
docker-compose up -d

echo "Waiting for services to be ready (30 seconds)..."
sleep 30

echo "Checking PostgreSQL health..."
if ! docker-compose exec postgres pg_isready -U postgres; then
  echo "PostgreSQL is not ready"
  exit 1
fi

echo "Checking Server health..."
SERVER_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/api/health)
if [ "$SERVER_STATUS" -ne 200 ]; then
  echo "Server health check failed with status: $SERVER_STATUS"
  docker-compose logs server
  exit 1
fi

echo "Checking Client health..."
CLIENT_STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
if [ "$CLIENT_STATUS" -ne 200 ]; then
  echo "Client health check failed with status: $CLIENT_STATUS"
  docker-compose logs client
  exit 1
fi

echo "All services are healthy!"
docker-compose logs --tail=20
docker-compose down
exit 0