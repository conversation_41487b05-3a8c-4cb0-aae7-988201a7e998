# Shadcn Template

A modern, full-stack web application template built with Next.js 15+, shadcn/ui, Auth.js v5, and PostgreSQL. This project provides a complete starter kit for building robust web applications with a beautiful UI, robust authentication, and a scalable backend.

[![Next.js 15](https://img.shields.io/badge/Next.js-15.1.7-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.3.3-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![shadcn/ui](https://img.shields.io/badge/shadcn%2Fui-latest-black?style=for-the-badge)](https://ui.shadcn.com/)
[![Auth.js v5](https://img.shields.io/badge/Auth.js-v5_beta-blue?style=for-the-badge)](https://authjs.dev/)
[![Prisma 6](https://img.shields.io/badge/Prisma-6.4.1-2D3748?style=for-the-badge&logo=prisma)](https://www.prisma.io/)
[![PostgreSQL 17](https://img.shields.io/badge/PostgreSQL-17-336791?style=for-the-badge&logo=postgresql)](https://www.postgresql.org/)

## 🌟 Features

- **Modern Stack**: Next.js 15+ with App Router, TypeScript, and React 19
- **Beautiful UI**: Powered by shadcn/ui components with Tailwind CSS
- **Robust Authentication**: Auth.js v5 with multi-provider support (Email/Password, Google, GitHub, Microsoft)
- **Admin Dashboard**: Separate admin panel with role-based access control
- **Database Integration**: PostgreSQL 17 with Prisma ORM 6.4+
- **Docker Ready**: Complete Docker setup for development and deployment
- **Comprehensive Testing**: Jest and React Testing Library setup
- **Internationalization**: Multi-language support via next-intl
- **Type Safety**: End-to-end type safety with TypeScript and Zod validation

## 📂 Project Structure

This project follows a monorepo structure with three main components:

```
shadcn-template/
├── client/             # Main client application (Next.js 15+, shadcn/ui)
│   ├── app/            # App router routes and pages
│   ├── components/     # React components, including shadcn/ui 
│   ├── lib/            # Utility functions and helpers
│   ├── auth/           # Auth.js v5 configuration
│   └── ...             # Other client-side code
├── client-admin/       # Admin panel (Next.js 15+, shadcn/ui)
│   ├── app/            # Admin app routes and pages
│   ├── components/     # Admin-specific components
│   └── ...             # Other admin-side code
├── server/             # Backend server (Express.js, Prisma)
│   ├── src/            # Server source code
│   ├── prisma/         # Prisma schema and migrations
│   └── ...             # Other server-side code
├── db-init/            # Database initialization scripts
├── docker-compose.yaml # Docker Compose configuration
└── ...                 # Configuration files and documentation
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- Docker and Docker Compose (for containerized setup)
- pnpm (for server) or yarn (for client applications)

### Quick Start with Docker (Recommended)

The easiest way to get started is using Docker Compose, which sets up all services including the database:

```bash
# Clone the repository
git clone https://github.com/yourusername/shadcn-template.git
cd shadcn-template

# Start all services
docker compose up -d
```

This will start:
- PostgreSQL database (port 5432)
- Backend server (port 3001)
- Main client app (port 3000)
- Admin client app (port 3002)

### Manual Setup

#### 1. Database Setup

```bash
# Install PostgreSQL 17 (platform-specific)
# Create a database named 'shadcn_template'
```

#### 2. Backend Server

```bash
cd server
pnpm install
pnpm prisma generate
pnpm prisma db push
pnpm prisma:seed  # Add initial data
pnpm dev
```

#### 3. Main Client

```bash
cd client
yarn install
yarn dev
```

#### 4. Admin Client

```bash
cd client-admin
yarn install
yarn dev
```

## 🔐 Authentication

This project uses Auth.js v5 (formerly NextAuth.js) with multiple authentication providers:

- **Email/Password**: Traditional email and password authentication
- **OAuth Providers**: Google, GitHub, Microsoft
- **Two-Factor Authentication**: Optional 2FA for enhanced security

### Default Users

When running the database initialization scripts, two default users are created:

1. **Admin User**
   - Email: <EMAIL>
   - Password: Admin@123
   - Role: Administrator with full access

2. **Regular User**
   - Email: <EMAIL>
   - Password: User@123
   - Role: Standard user with limited permissions

## 🗄️ Database Structure

The database uses PostgreSQL 17 with Prisma ORM and includes the following key models:

- **User**: User accounts with authentication details
- **Account**: OAuth provider accounts linked to users
- **Session**: User sessions with activity tracking
- **Role**: User roles (admin, user, etc.)
- **Permission**: Granular permissions assigned to roles
- **Application**: Application modules available in the system
- **MenuItem**: Navigation menu items for applications

### Database Diagram

```
User 1--* Account
User 1--* Session
User *--* Role (through UserRole)
Role *--* Permission (through RolePermission)
Role *--* Application (through RoleApplication)
Application 1--* MenuItem
```

## 🔧 Configuration

### Environment Variables

#### Server (.env)

```
DATABASE_URL=********************************************/shadcn_template?schema=public
CLIENT_URL=http://localhost:3000
ADMIN_URL=http://localhost:3002
SESSION_SECRET=your-session-secret
PASSWORD_SALT=your-password-salt
AUTH_SECRET=your-auth-secret
```

#### Client (.env.local)

```
NEXT_PUBLIC_API_URL=http://localhost:3001
AUTH_TRUST_HOST=true
AUTH_SECRET=your-auth-secret
```

#### Admin Client (.env.local)

```
NEXT_PUBLIC_API_URL=http://localhost:3001
AUTH_TRUST_HOST=true
AUTH_SECRET=your-auth-secret
```

## 🧪 Testing

The project includes testing setup for all components:

### Backend Tests

```bash
cd server
pnpm test
```

### Client Tests

```bash
cd client
yarn test
```

### Admin Client Tests

```bash
cd client-admin
yarn test
```

## 🌐 Deployment

### Docker Deployment

For production deployment, use Docker Compose with production build targets:

```bash
docker compose -f docker-compose.prod.yaml up -d
```

### Manual Deployment

#### Backend

```bash
cd server
pnpm build
pnpm start
```

#### Clients

```bash
cd client
yarn build
yarn start

cd client-admin
yarn build
yarn start
```

## 🔍 Key Directories & Files

### Client Application

- `client/app/`: Next.js 15+ App Router pages and routes
- `client/components/`: UI components including shadcn/ui components
- `client/lib/`: Utility functions and shared code
- `client/auth.ts`: Auth.js v5 configuration
- `client/auth.config.ts`: Authentication strategy configuration

### Admin Panel

- `client-admin/app/`: Admin panel pages and routes
- `client-admin/components/`: Admin-specific UI components
- `client-admin/lib/`: Admin utility functions

### Server

- `server/src/`: Server source code
- `server/prisma/`: Database schema and migrations
- `server/src/routes/`: API endpoints
- `server/src/controllers/`: Business logic controllers
- `server/src/middleware/`: Express middleware

## 🛠️ Technology Stack

### Frontend

- **Framework**: Next.js 15.1.7
- **UI Library**: shadcn/ui with Tailwind CSS
- **State Management**: React Context API, Redux Toolkit
- **Authentication**: Auth.js v5 beta
- **Form Handling**: React Hook Form with Zod validation
- **HTTP Client**: Fetch API with custom wrappers

### Backend

- **Framework**: Express.js 5.1.0
- **Database ORM**: Prisma 6.4.1
- **Authentication**: JWT with Auth.js v5
- **Validation**: Zod schemas
- **Password Hashing**: Argon2
- **Logging**: Winston

### Database

- **DBMS**: PostgreSQL 17
- **ORM**: Prisma ORM 6.4.1
- **Migration**: Prisma migrations
- **Seeding**: Custom seed scripts

## 👥 Role-Based Access Control

The application implements a comprehensive role-based access control system:

- **Roles**: Admin, User (extensible)
- **Permissions**: Granular permissions for each role
- **Applications**: Different application modules accessible based on roles
- **Menu Items**: Dynamic navigation based on user permissions

## 🌍 Internationalization

The project supports multiple languages through next-intl:

- English (default)
- Other languages can be added via the localization system

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📜 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📬 Contact

If you have any questions or suggestions, please open an issue or contact the maintainers.

---

Built with ❤️ using Next.js, shadcn/ui, and Auth.js v5.
