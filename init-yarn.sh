#!/bin/bash

# This script initializes yarn in each directory independently

echo "Initializing yarn in project directories..."

# First, clean up any existing lock files
find . -name "package-lock.json" -type f -delete
find . -name "pnpm-lock.yaml" -type f -delete
find . -name "yarn.lock" -type f -delete

# Root directory
echo "Setting up root directory..."
cd /Users/<USER>/Documents/GitHub/shadcn-template
rm -f .yarnrc .yarnrc.yml
yarn init -y

# Client directory
echo "Setting up client directory..."
cd /Users/<USER>/Documents/GitHub/shadcn-template/client
rm -f .yarnrc .yarnrc.yml
# Extract the dependencies from package.json
DEPS=$(node -e "const pkg=require('./package.json'); console.log(Object.keys(pkg.dependencies || {}).map(d => d+'@\"'+pkg.dependencies[d]+'\"').join(' '))")
DEV_DEPS=$(node -e "const pkg=require('./package.json'); console.log(Object.keys(pkg.devDependencies || {}).map(d => d+'@\"'+pkg.devDependencies[d]+'\"').join(' '))")

# Initialize a new yarn project
yarn init -y
# Merge package.json scripts
node -e "const fs=require('fs'); const pkg=require('./package.json'); const oldPkg=JSON.parse(fs.readFileSync('./package.json.bak', 'utf8')); pkg.scripts=oldPkg.scripts; pkg.overrides=oldPkg.overrides; fs.writeFileSync('./package.json', JSON.stringify(pkg, null, 2))"

# Server directory
echo "Setting up server directory..."
cd /Users/<USER>/Documents/GitHub/shadcn-template/server
rm -f .yarnrc .yarnrc.yml
# Extract the dependencies from package.json
DEPS=$(node -e "const pkg=require('./package.json'); console.log(Object.keys(pkg.dependencies || {}).map(d => d+'@\"'+pkg.dependencies[d]+'\"').join(' '))")
DEV_DEPS=$(node -e "const pkg=require('./package.json'); console.log(Object.keys(pkg.devDependencies || {}).map(d => d+'@\"'+pkg.devDependencies[d]+'\"').join(' '))")

# Initialize a new yarn project
yarn init -y
# Merge package.json scripts
node -e "const fs=require('fs'); const pkg=require('./package.json'); const oldPkg=JSON.parse(fs.readFileSync('./package.json.bak', 'utf8')); pkg.scripts=oldPkg.scripts; fs.writeFileSync('./package.json', JSON.stringify(pkg, null, 2))"

echo "Yarn initialization complete. Now you can install dependencies in each directory using 'yarn install'"
