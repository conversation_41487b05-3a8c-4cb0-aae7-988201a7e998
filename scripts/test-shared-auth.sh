#!/bin/bash

# Colors for better readability
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print section headers
print_header() {
  echo -e "\n${BLUE}======================================${NC}"
  echo -e "${BLUE}   $1${NC}"
  echo -e "${BLUE}======================================${NC}\n"
}

# Function to handle errors
handle_error() {
  echo -e "${RED}ERROR: $1${NC}"
  if [ "$2" = "exit" ]; then
    exit 1
  fi
}

# Function to run a command and check for errors
run_command() {
  echo -e "${YELLOW}Running: $1${NC}"
  eval $1
  if [ $? -ne 0 ]; then
    handle_error "$1 failed"
    if [ "$2" = "exit" ]; then
      exit 1
    fi
    return 1
  fi
  return 0
}

# Start the testing process
print_header "SHARED AUTHENTICATION TESTING"
echo -e "${YELLOW}This script will test shared authentication between client and client-admin.${NC}"
echo -e "${YELLOW}Make sure you have all dependencies installed before running this script.${NC}"
echo

# Check if we're in the right directory
if [ ! -d "client" ] || [ ! -d "client-admin" ]; then
  handle_error "Please run this script from the project root directory." "exit"
fi

# Check for required tools
print_header "CHECKING PREREQUISITES"

# Check for Node.js
if ! command -v node &> /dev/null; then
  handle_error "Node.js is not installed. Please install Node.js before running this script." "exit"
else
  node_version=$(node -v)
  echo -e "${GREEN}Node.js is installed: $node_version${NC}"
fi

# Check for npm/yarn
if command -v yarn &> /dev/null; then
  package_manager="yarn"
  echo -e "${GREEN}Yarn is installed.${NC}"
elif command -v npm &> /dev/null; then
  package_manager="npm"
  echo -e "${GREEN}NPM is installed.${NC}"
else
  handle_error "Neither yarn nor npm is installed. Please install a package manager." "exit"
fi

# Check for environment variables
print_header "CHECKING ENVIRONMENT VARIABLES"

# Check client .env.local
if [ ! -f "client/.env.local" ]; then
  handle_error "client/.env.local not found. Please create it before running this script." "exit"
else
  echo -e "${GREEN}client/.env.local found.${NC}"
  
  # Check for COOKIE_DOMAIN
  if ! grep -q "COOKIE_DOMAIN" "client/.env.local"; then
    handle_error "COOKIE_DOMAIN not found in client/.env.local. Please add it before running this script." "exit"
  else
    cookie_domain=$(grep "COOKIE_DOMAIN" "client/.env.local" | cut -d'=' -f2)
    echo -e "${GREEN}COOKIE_DOMAIN found in client/.env.local: $cookie_domain${NC}"
  fi
fi

# Check client-admin .env.local
if [ ! -f "client-admin/.env.local" ]; then
  handle_error "client-admin/.env.local not found. Please create it before running this script." "exit"
else
  echo -e "${GREEN}client-admin/.env.local found.${NC}"
  
  # Check for COOKIE_DOMAIN
  if ! grep -q "COOKIE_DOMAIN" "client-admin/.env.local"; then
    handle_error "COOKIE_DOMAIN not found in client-admin/.env.local. Please add it before running this script." "exit"
  else
    admin_cookie_domain=$(grep "COOKIE_DOMAIN" "client-admin/.env.local" | cut -d'=' -f2)
    echo -e "${GREEN}COOKIE_DOMAIN found in client-admin/.env.local: $admin_cookie_domain${NC}"
  fi
fi

# Check if COOKIE_DOMAIN is the same in both files
if [ "$cookie_domain" != "$admin_cookie_domain" ]; then
  handle_error "COOKIE_DOMAIN is different in client/.env.local and client-admin/.env.local. Please make them the same." "exit"
else
  echo -e "${GREEN}COOKIE_DOMAIN is the same in both files.${NC}"
fi

# Check auth.config.ts files
print_header "CHECKING AUTH CONFIGURATION"

# Check client auth.config.ts
if [ ! -f "client/auth.config.ts" ]; then
  handle_error "client/auth.config.ts not found. Please create it before running this script." "exit"
else
  echo -e "${GREEN}client/auth.config.ts found.${NC}"
  
  # Check for domain in cookies configuration
  if ! grep -q "domain: process.env.COOKIE_DOMAIN" "client/auth.config.ts"; then
    handle_error "domain: process.env.COOKIE_DOMAIN not found in client/auth.config.ts. Please add it before running this script." "exit"
  else
    echo -e "${GREEN}domain: process.env.COOKIE_DOMAIN found in client/auth.config.ts.${NC}"
  fi
fi

# Check client-admin auth.config.ts
if [ ! -f "client-admin/auth.config.ts" ]; then
  handle_error "client-admin/auth.config.ts not found. Please create it before running this script." "exit"
else
  echo -e "${GREEN}client-admin/auth.config.ts found.${NC}"
  
  # Check for domain in cookies configuration
  if ! grep -q "domain: process.env.COOKIE_DOMAIN" "client-admin/auth.config.ts"; then
    handle_error "domain: process.env.COOKIE_DOMAIN not found in client-admin/auth.config.ts. Please add it before running this script." "exit"
  else
    echo -e "${GREEN}domain: process.env.COOKIE_DOMAIN found in client-admin/auth.config.ts.${NC}"
  fi
fi

# Start the applications
print_header "STARTING APPLICATIONS"
echo -e "${YELLOW}Starting client and client-admin applications...${NC}"

# Start client
echo -e "${YELLOW}Starting client...${NC}"
cd client
if [ "$package_manager" = "yarn" ]; then
  yarn dev > /dev/null 2>&1 &
else
  npm run dev > /dev/null 2>&1 &
fi
CLIENT_PID=$!
cd ..

# Start client-admin
echo -e "${YELLOW}Starting client-admin...${NC}"
cd client-admin
if [ "$package_manager" = "yarn" ]; then
  yarn dev > /dev/null 2>&1 &
else
  npm run dev > /dev/null 2>&1 &
fi
CLIENT_ADMIN_PID=$!
cd ..

# Wait for applications to start
echo -e "${YELLOW}Waiting for applications to start...${NC}"
sleep 10

# Test shared authentication
print_header "TESTING SHARED AUTHENTICATION"
echo -e "${YELLOW}Please follow these steps to test shared authentication:${NC}"
echo -e "1. Open http://localhost:3000/auth/login in your browser"
echo -e "2. Log in with your credentials"
echo -e "3. After successful login, open http://localhost:3001/admin in another tab"
echo -e "4. If shared authentication is working, you should be automatically logged in to the admin panel"
echo -e "5. If you're not automatically logged in, check the console logs for errors"

# Wait for user to test
echo -e "${YELLOW}Press Enter when you're done testing...${NC}"
read -r

# Clean up
print_header "CLEANING UP"
echo -e "${YELLOW}Stopping applications...${NC}"
kill $CLIENT_PID
kill $CLIENT_ADMIN_PID

print_header "TESTING COMPLETED"
echo -e "${GREEN}Shared authentication testing completed.${NC}"
echo -e "${YELLOW}If you encountered any issues, please check the error messages above.${NC}"
