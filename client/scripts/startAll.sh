#!/bin/bash

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null
then
    echo "pnpm could not be found"
    echo "Installing pnpm globally..."
    npm install -g pnpm
    echo "pnpm installed successfully."
fi

echo "Starting all servers..."

# Function to start a directory
start_dir() {
    local dir="$1"
    echo "Starting: $dir"
    cd "$dir"
    pnpm start &
    cd "-"
    echo "$dir starting..."
}

# Start client
start_dir "client"

# Start client-admin
start_dir "client-admin"

# Start server
start_dir "server"

echo "All servers are starting."
fi