#!/bin/bash

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null
then
    echo "pnpm could not be found"
    echo "Installing pnpm globally..."
    npm install -g pnpm
    echo "pnpm installed successfully."
fi

echo "Compiling client and client-admin..."

# Function to compile a directory
compile_dir() {
    local dir="$1"
    echo "Compiling: $dir"
    cd "$dir"
    pnpm run build
    cd "-"
}

# Compile client
compile_dir "client"

# Compile client-admin
compile_dir "client-admin"

echo "Compilation completed."

fi

# Build the micro-frontend application
print_app_step "Micro Frontend" "Building the application..."
cd "${MICRO_FRONTEND_DIR}" && pnpm run build --no-lint || handle_error "Failed to build micro-frontend application"

print_app_step "Micro Frontend" "Compilation completed successfully! 🎉"

#######################################
# Summary
#######################################
print_step "All applications have been successfully compiled! 🚀"
print_step "To run the applications:"
echo -e "  Main App: ${GREEN}cd ${ROOT_DIR} && pnpm start${NC}"
echo -e "  Micro Frontend: ${GREEN}cd ${MICRO_FRONTEND_DIR} && pnpm start${NC}"
echo ""
print_step "For development mode:"
echo -e "  Main App: ${GREEN}cd ${ROOT_DIR} && pnpm dev${NC}"
echo -e "  Micro Frontend: ${GREEN}cd ${MICRO_FRONTEND_DIR} && pnpm dev${NC}"

# Make script executable
chmod +x "${ROOT_DIR}/scripts/comboCompile.sh"