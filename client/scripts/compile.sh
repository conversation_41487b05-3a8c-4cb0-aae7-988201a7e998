#!/bin/bash

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null
then
    echo "pnpm could not be found"
    echo "Installing pnpm globally..."
    npm install -g pnpm
    echo "pnpm installed successfully."
fi

# Function to compile a directory
compile_dir() {
    local dir="$1"
    echo "Compiling: $dir"
    cd "$dir"
    pnpm run build
    cd "-"
}

# Compile client
compile_dir "client"

echo "Client compilation completed."
