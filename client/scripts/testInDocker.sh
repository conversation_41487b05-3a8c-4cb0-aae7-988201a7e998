#!/bin/bash

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null
then
    echo "pnpm could not be found"
    echo "Installing pnpm globally..."
    npm install -g pnpm
    echo "pnpm installed successfully."
fi

echo "Testing client in Docker..."

# Function to run commands and handle errors
run_or_exit() {
    echo "Running: $@"
    "$@" || exit $?
}

# Navigate to client directory
cd "client"

# Build the Docker image for testing
run_or_exit docker build -f Dockerfile.test -t client-test .

# Run the tests in the Docker container
run_or_exit docker run --rm client-test

echo "Client tests in Docker completed."