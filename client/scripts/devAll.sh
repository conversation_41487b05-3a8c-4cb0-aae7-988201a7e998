#!/bin/bash

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null
then
    echo "pnpm could not be found"
    echo "Installing pnpm globally..."
    npm install -g pnpm
    echo "pnpm installed successfully."
fi

echo "Starting development servers for client and client-admin..."

# Start client in dev mode
cd "client"
echo "Starting client development server..."
pnpm run dev &
CLIENT_PID=$!

# Start client-admin in dev mode
cd "../client-admin"
echo "Starting client-admin development server..."
pnpm run dev &
CLIENT_ADMIN_PID=$!

# Start server in dev mode
cd "../../server"
echo "Starting server development server..."
pnpm run start:dev &
SERVER_PID=$!

echo "All development servers started."

# Trap exit signals to clean up child processes
trap 'kill $CLIENT_PID $CLIENT_ADMIN_PID $SERVER_PID; exit' SIGINT SIGTERM

# Wait indefinitely
wait