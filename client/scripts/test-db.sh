#!/bin/bash

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null
then
    echo "pnpm could not be found"
    echo "Installing pnpm globally..."
    npm install -g pnpm
    echo "pnpm installed successfully."
}

# Function to run commands and handle errors
run_or_exit() {
    echo "Running: $@"
    "$@" || exit $?
}

echo "Testing client database..."

# Navigate to client directory and run database tests
cd "client"
run_or_exit pnpm test db

echo "Client database tests completed."
