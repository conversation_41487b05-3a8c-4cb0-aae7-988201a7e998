# Error Handling System

## 1. Overview

This document details the error handling mechanisms implemented in the system, including frontend error handling, backend error handling, API error handling, and the strategies for implementing each.

## 2. Error Types

### 2.1 Authentication Errors

```typescript
const ERROR_MESSAGES = {
  AccessDenied: 'Access Denied. Please contact support. Or try logging with <PERSON><PERSON> and password.',
  OAuthAccountNotLinked:
    'Another account already exists with the same e-mail address. Please try logging in with a different provider.',
  default: 'Oops something went wrong!',
};
```

#### Error Categories

- Access Denied
- OAuth Account Not Linked
- Default Error

### 2.2 API Errors

```typescript
interface ApiError {
  code: string;
  message: string;
  details?: unknown;
}
```

#### Standard Error Codes

- `DB_CONNECTION_ERROR`: Database connection error
- `USER_NOT_FOUND`: User does not exist
- `INVALID_CREDENTIALS`: Invalid authentication information
- `VALIDATION_ERROR`: Data validation error
- `OPERATION_FAILED`: Operation execution failed

## 3. Error Handling Mechanisms

### 3.1 Frontend Error Handling

#### Form Errors

```typescript
const onSubmit = async (data: LoginFormData) => {
  setError(undefined);
  setSuccess(undefined);

  try {
    const result = await loginAction(data);
    if ('error' in result) {
      setError(result.error);
    } else if ('success' in result) {
      setSuccess(result.success);
    }
  } catch (error) {
    setError('An unexpected error occurred');
  }
};
```

#### Features

- Clear previous error states
- Use try-catch to capture errors
- Display user-friendly error messages
- Support for success/error state switching

### 3.2 Backend Error Handling

#### Database Errors

```typescript
export async function loginUser(credentials: LoginCredentials): Promise<User> {
  const user = await prisma.user.findUnique({
    where: { email: credentials.email },
    include: { loginMethods: true },
  });

  if (!user) {
    throw new Error('User not found');
  }

  // ... other validation logic
}
```

#### Features

- Detailed error checking
- Clear error messages
- Type-safe error handling
- Transaction rollback support

### 3.3 API Error Handling

#### Response Format

```typescript
interface ErrorResponse {
  success: false;
  error: ApiError;
}
```

#### Features

- Standardized error responses
- HTTP status code mapping
- Detailed error information
- Support for debugging information

## 4. Error Display

### 4.1 Error Components

```typescript
const AuthErrorForm: React.FC<AuthErrorFormProps> = ({ defaultError = "An unknown error occurred" }) => {
    const searchParams = useSearchParams();
    const [error, setError] = useState<string | null>(defaultError);

    useEffect(() => {
        if (searchParams) {
            const errorParam = searchParams.get("error");
            setError(errorParam || defaultError);
        }
    }, [searchParams, defaultError]);

    return (
        <div className="error-container">
            <p className="error-message">{error}</p>
        </div>
    );
};
```

#### Features

- Responsive error display
- Support for default error messages
- URL parameter error handling
- Clear error presentation

### 4.2 Error Pages

```typescript
const AuthErrorPage: React.FC = () => {
    return (
        <div className="flex justify-center items-center min-h-screen bg-background">
            <Card className="w-full max-w-md">
                <CardContent>
                    <Suspense fallback={<div className="text-center p-4">Loading...</div>}>
                        <AuthErrorForm />
                    </Suspense>
                </CardContent>
            </Card>
        </div>
    );
};
```

#### Features

- Dedicated error pages
- Elegant loading states
- Responsive design
- User-friendly interface

## 5. Logging

### 5.1 Error Logs

```typescript
error: (message: string, meta?: unknown) => {
  console.error(`[ERROR] ${message}`, meta ? JSON.stringify(meta) : '');
};
```

#### Logged Content

- Error message
- Timestamp
- Stack trace
- Context information

### 5.2 Log Levels

- ERROR: Severe errors
- WARN: Warning information
- INFO: General information
- DEBUG: Debugging information

## 6. Security Considerations

### 6.1 Error Message Security

- Avoid exposing sensitive information in error messages
- Use generic error messages for users
- Log detailed errors for debugging
- Implement proper error handling for security-related operations

### 6.2 Error Handling for Security Features

- Authentication errors
- Authorization errors
- Input validation errors
- Rate limiting errors

## 7. Implementation Examples

### 7.1 Global Error Boundary

```tsx
import { ErrorBoundary } from 'react-error-boundary';

const fallbackRender = ({ error, resetErrorBoundary }) => {
  return (
    <div className="error-boundary">
      <h2>Something went wrong:</h2>
      <p>{error.message}</p>
      <button onClick={resetErrorBoundary}>Try again</button>
    </div>
  );
};

export const AppErrorBoundary = ({ children }) => {
  return (
    <ErrorBoundary fallbackRender={fallbackRender}>
      {children}
    </ErrorBoundary>
  );
};
```

### 7.2 API Error Handler

```typescript
export const apiErrorHandler = (error: unknown): ApiError => {
  if (error instanceof PrismaClientKnownRequestError) {
    return {
      code: 'DB_ERROR',
      message: 'Database operation failed',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    };
  }

  if (error instanceof ZodError) {
    return {
      code: 'VALIDATION_ERROR',
      message: 'Invalid input data',
      details: process.env.NODE_ENV === 'development' ? error.errors : undefined,
    };
  }

  // Default error
  return {
    code: 'INTERNAL_ERROR',
    message: 'An unexpected error occurred',
    details: process.env.NODE_ENV === 'development' ? String(error) : undefined,
  };
};
```

## 8. Best Practices

### 8.1 Frontend

- Use error boundaries for React components
- Implement form validation with clear error messages
- Handle network errors gracefully
- Provide user-friendly error messages

### 8.2 Backend

- Use try-catch blocks for error handling
- Implement proper error logging
- Return standardized error responses
- Handle database errors properly

### 8.3 API

- Use HTTP status codes correctly
- Provide detailed error messages in development
- Implement rate limiting with appropriate error responses
- Document API error responses

## 9. Testing Error Handling

### 9.1 Unit Tests

```typescript
describe('Error handling', () => {
  it('should handle validation errors', async () => {
    const result = await loginAction({ email: 'invalid', password: '123' });
    expect(result).toHaveProperty('error');
    expect(result.error).toContain('Invalid email format');
  });

  it('should handle server errors', async () => {
    // Mock server error
    server.use(
      rest.post('/api/login', (req, res, ctx) => {
        return res(ctx.status(500));
      })
    );

    const result = await loginAction({ email: '<EMAIL>', password: 'password123' });
    expect(result).toHaveProperty('error');
    expect(result.error).toContain('An unexpected error occurred');
  });
});
```

### 9.2 Integration Tests

- Test error handling across components
- Verify error messages are displayed correctly
- Test error recovery mechanisms
- Ensure proper logging of errors

## 10. Future Improvements

- Implement centralized error tracking
- Add error analytics
- Improve error recovery mechanisms
- Enhance error documentation
