# State Management

## Overview

This document outlines the state management system used in the application, which is built using Redux Toolkit and Redux Persist for client-side state management.

## Architecture

### 1. Store Configuration

```typescript
// store/store.ts
import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import rootReducer from './rootReducer';

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['auth', 'settings'], // Persist only specific slices
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);
```

### 2. Root Reducer

```typescript
// store/rootReducer.ts
import { combineReducers } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import settingsReducer from './slices/settingsSlice';

const rootReducer = combineReducers({
  auth: authReducer,
  settings: settingsReducer,
});

export default rootReducer;
```

## State Slices

### 1. Authentication Slice

```typescript
// store/slices/authSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  loading: false,
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload;
    },
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});
```

### 2. Settings Slice

```typescript
// store/slices/settingsSlice.ts
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface SettingsState {
  theme: 'light' | 'dark';
  notifications: boolean;
  autoSave: boolean;
}

const initialState: SettingsState = {
  theme: 'light',
  notifications: true,
  autoSave: true,
};

const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
    },
    toggleNotifications: (state) => {
      state.notifications = !state.notifications;
    },
    setAutoSave: (state, action: PayloadAction<boolean>) => {
      state.autoSave = action.payload;
    },
  },
});
```

## Implementation Details

### 1. Store Provider Setup

```typescript
// app/providers.tsx
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '@/store/store';

export function Providers({ children }: { children: React.ReactNode }) {
    return (
        <Provider store={store}>
            <PersistGate loading={null} persistor={persistor}>
                {children}
            </PersistGate>
        </Provider>
    );
}
```

### 2. State Usage in Components

```typescript
// Example component using Redux state
import { useSelector, useDispatch } from 'react-redux';
import { setTheme } from '@/store/slices/settingsSlice';

const ThemeToggle = () => {
    const theme = useSelector((state) => state.settings.theme);
    const dispatch = useDispatch();

    const toggleTheme = () => {
        dispatch(setTheme(theme === 'light' ? 'dark' : 'light'));
    };

    return (
        <button onClick={toggleTheme}>
            Toggle Theme
        </button>
    );
};
```

## State Management Patterns

### 1. Action Creators

- Use Redux Toolkit's createSlice for automatic action creators
- Implement thunks for async operations
- Handle side effects in thunks

### 2. Selectors

- Create memoized selectors using createSelector
- Implement reusable selector functions
- Use selector composition for complex state derivation

### 3. State Updates

- Immutable updates using Redux Toolkit's createSlice
- Batch updates when necessary
- Handle optimistic updates for better UX

## Performance Optimization

### 1. Memoization

```typescript
// Example of memoized selector
import { createSelector } from '@reduxjs/toolkit';

const selectAuthState = (state) => state.auth;

export const selectCurrentUser = createSelector(
  [selectAuthState],
  (auth) => auth.user
);
```

### 2. Avoiding Unnecessary Renders

- Use shallowEqual for object comparisons
- Select only needed state parts
- Implement React.memo for components

### 3. Redux DevTools Integration

- Enable development tools
- Monitor state changes
- Debug state issues

## Async Operations

### 1. Thunks

```typescript
// Example thunk for login
import { createAsyncThunk } from '@reduxjs/toolkit';
import { authApi } from '@/api/auth';

export const loginUser = createAsyncThunk(
  'auth/login',
  async (credentials, { rejectWithValue }) => {
    try {
      const response = await authApi.login(credentials);
      return response.data;
    } catch (error) {
      return rejectWithValue(error.response.data);
    }
  }
);
```

### 2. Error Handling

- Centralized error handling
- Consistent error state management
- User-friendly error messages

### 3. Loading States

- Track loading states for async operations
- Show loading indicators
- Handle timeout scenarios

## State Persistence

### 1. Redux Persist Configuration

- Selective persistence with whitelist/blacklist
- Storage engine configuration
- Migration strategies

### 2. Security Considerations

- Sensitive data handling
- Encryption options
- Session vs. persistent storage

## Testing Strategy

### 1. Unit Tests

```typescript
// Example test for a reducer
import authReducer, { setUser, logout } from './authSlice';

describe('auth reducer', () => {
  it('should handle initial state', () => {
    expect(authReducer(undefined, { type: 'unknown' })).toEqual({
      isAuthenticated: false,
      user: null,
      token: null,
      loading: false,
      error: null,
    });
  });

  it('should handle setUser', () => {
    const user = { id: '1', name: 'Test User' };
    const actual = authReducer(initialState, setUser(user));
    expect(actual.user).toEqual(user);
    expect(actual.isAuthenticated).toEqual(true);
  });

  it('should handle logout', () => {
    const startState = {
      isAuthenticated: true,
      user: { id: '1', name: 'Test User' },
      token: 'token',
      loading: false,
      error: null,
    };
    const actual = authReducer(startState, logout());
    expect(actual.user).toEqual(null);
    expect(actual.token).toEqual(null);
    expect(actual.isAuthenticated).toEqual(false);
  });
});
```

### 2. Integration Tests

- Test thunks with mock API
- Verify state changes
- Test component integration

## Dependencies

```json
{
  "dependencies": {
    "@reduxjs/toolkit": "^2.2.0",
    "react-redux": "^9.1.0",
    "redux-persist": "^6.0.0"
  }
}
```
