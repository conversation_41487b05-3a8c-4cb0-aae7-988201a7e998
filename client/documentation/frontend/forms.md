# Form Validation System

## Overview

This document outlines the form validation system used in the application, which is built using Zod schema validation library integrated with React Hook Form.

## Validation Schemas

### 1. Login Schema

```typescript
const LoginSchema = z.object({
  email: z.string().email({
    message: 'Invalid email format',
  }),
  password: z.string().min(1, {
    message: 'Password is required',
  }),
  twoFactorCode: z.optional(z.string()),
});
```

### 2. Registration Schema

```typescript
const RegisterSchema = z.object({
  email: z.string().email({
    message: 'Email is required and must be a valid email format',
  }),
  password: z
    .string()
    .min(10, { message: 'Password must be at least 10 characters long' })
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{10,}$/, {
      message:
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    }),
  name: z.string().min(1, {
    message: 'Name is required',
  }),
});
```

### 3. Password Reset Schema

```typescript
const ResetPasswordSchema = z.object({
  email: z.string().email({
    message: 'Invalid email format',
  }),
});
```

### 4. New Password Schema

```typescript
const NewPasswordSchema = z
  .object({
    password: z
      .string()
      .min(10, { message: 'Password must be at least 10 characters long' })
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{10,}$/, {
        message:
          'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
      }),
    confirmPassword: z.string().min(1, 'Confirm password is required'),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords do not match',
    path: ['confirmPassword'],
  });
```

## Implementation Details

### Form Integration

- Uses React Hook Form for form state management
- Integrates Zod schemas using @hookform/resolvers/zod
- Provides real-time validation feedback
- Handles form submission and error states

### Error Handling

1. **Client-Side Validation**

   - Immediate feedback on input errors
   - Custom error messages for each validation rule
   - Visual indicators for invalid fields

2. **Server-Side Validation**
   - Double-checks validation rules on the server
   - Returns specific error messages for failed validations
   - Handles edge cases not covered by client-side validation

### UI Components

1. **Form Error Component**

   - Displays validation errors in a consistent format
   - Uses alert component for error messages
   - Supports multiple error messages

2. **Form Success Component**
   - Shows success messages after successful form submission
   - Automatically dismisses after a set duration
   - Provides visual feedback for successful actions

## Password Requirements

- Minimum length: 10 characters
- Must contain at least:
  - One uppercase letter
  - One lowercase letter
  - One number
  - One special character (@$!%\*?&)

## Security Considerations

1. **Password Security**

   - Strong password requirements
   - Password strength indicator
   - Secure password hashing

2. **Input Sanitization**
   - Validation against XSS attacks
   - Email format verification
   - Special character handling

## Performance Optimization

1. **Validation Efficiency**

   - Client-side validation to reduce server load
   - Debounced validation for real-time feedback
   - Optimized regex patterns

2. **Error Handling**
   - Efficient error message management
   - Optimized re-rendering on validation
   - Cached validation results

## Testing Strategy

1. **Unit Tests**

   - Individual validation rule testing
   - Error message verification
   - Edge case handling

2. **Integration Tests**
   - Form submission flow testing
   - Error handling verification
   - Success scenario validation

## Example Implementation

```tsx
'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useState, useTransition } from 'react';

import { LoginSchema } from '@/schemas';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FormError } from '@/components/form-error';
import { FormSuccess } from '@/components/form-success';
import { login } from '@/actions/auth';

export const LoginForm = () => {
  const [error, setError] = useState<string | undefined>('');
  const [success, setSuccess] = useState<string | undefined>('');
  const [isPending, startTransition] = useTransition();

  const form = useForm<z.infer<typeof LoginSchema>>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = (values: z.infer<typeof LoginSchema>) => {
    setError('');
    setSuccess('');

    startTransition(() => {
      login(values)
        .then((data) => {
          if (data.error) {
            setError(data.error);
          }

          if (data.success) {
            setSuccess(data.success);
          }
        })
        .catch(() => setError('Something went wrong'));
    });
  };

  return (
    <div className="space-y-6">
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <Input
            {...form.register('email')}
            placeholder="Email"
            type="email"
            disabled={isPending}
          />
          {form.formState.errors.email && (
            <span className="text-sm text-red-500">
              {form.formState.errors.email.message}
            </span>
          )}
        </div>
        <div className="space-y-2">
          <Input
            {...form.register('password')}
            placeholder="Password"
            type="password"
            disabled={isPending}
          />
          {form.formState.errors.password && (
            <span className="text-sm text-red-500">
              {form.formState.errors.password.message}
            </span>
          )}
        </div>
        <FormError message={error} />
        <FormSuccess message={success} />
        <Button
          type="submit"
          className="w-full"
          disabled={isPending}
        >
          {isPending ? 'Loading...' : 'Login'}
        </Button>
      </form>
    </div>
  );
};
```

## Dependencies

```json
{
  "dependencies": {
    "@hookform/resolvers": "^3.9.0",
    "react-hook-form": "^7.53.0",
    "zod": "^3.23.8"
  }
}
```
