# Component System

## Overview

This document outlines the component architecture and implementation details for the application, which uses shadcn/ui components and follows a modular, reusable design pattern.

## Component Architecture

### 1. Directory Structure

```
components/
├── auth/
│   ├── LoginForm.tsx
│   ├── RegisterForm.tsx
│   ├── ResetPasswordForm.tsx
│   └── common/
│       ├── AuthCardWrapper.tsx
│       ├── FormError.tsx
│       ├── FormSuccess.tsx
│       └── PasswordStrengthMeter.tsx
├── ui/
│   ├── input.tsx
│   ├── button.tsx
│   ├── label.tsx
│   └── alert.tsx
└── layout/
    ├── Header.tsx
    ├── Footer.tsx
    └── Sidebar.tsx
```

## Authentication Components

### 1. LoginForm

```typescript
interface LoginFormProps {
  onSuccess?: () => void;
  redirectUrl?: string;
}

const LoginForm: React.FC<LoginFormProps> = ({
  onSuccess,
  redirectUrl = DEFAULT_LOGIN_REDIRECT,
}) => {
  // Form state and handlers
  // Integration with React Hook Form and Zod
  // Error handling and success feedback
  // Two-factor authentication support
};
```

### 2. RegisterForm

```typescript
interface RegisterFormProps {
  onSuccess?: () => void;
}

const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess }) => {
  // Registration form implementation
  // Password strength validation
  // Email verification flow
};
```

### 3. ResetPasswordForm

```typescript
interface ResetPasswordFormProps {
  token?: string;
}

const ResetPasswordForm: React.FC<ResetPasswordFormProps> = ({ token }) => {
  // Password reset implementation
  // Token validation
  // New password requirements check
};
```

## Common Components

### 1. AuthCardWrapper

```typescript
interface AuthCardWrapperProps {
  children: React.ReactNode;
  headerLabel: string;
  backButtonLabel: string;
  backButtonHref: string;
  showSocial?: boolean;
}

const AuthCardWrapper: React.FC<AuthCardWrapperProps> = ({
  children,
  headerLabel,
  backButtonLabel,
  backButtonHref,
  showSocial,
}) => {
  // Authentication card layout
  // Social login buttons
  // Navigation elements
};
```

### 2. FormError/Success

```typescript
interface FormMessageProps {
  message?: string;
}

const FormError: React.FC<FormMessageProps> = ({ message }) => {
  // Error message display
  // Alert component integration
};

const FormSuccess: React.FC<FormMessageProps> = ({ message }) => {
  // Success message display
  // Auto-dismiss functionality
};
```

## UI Components (shadcn/ui)

### 1. Input Component

```typescript
interface InputProps
    extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
    ({ className, type, ...props }, ref) => {
        return (
            <input
                type={type}
                className={cn(
                    "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
                    className
                )}
                ref={ref}
                {...props}
            />
        )
    }
)
```

### 2. Button Component

```typescript
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    // Button implementation with variants
    // Loading state support
    // Icon integration
  }
);
```

## Layout Components

### 1. Header

```typescript
interface HeaderProps {
  user?: User;
  onLogout?: () => void;
}

const Header: React.FC<HeaderProps> = ({ user, onLogout }) => {
  // Navigation menu
  // User profile
  // Theme toggle
};
```

### 2. Sidebar

```typescript
interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  // Navigation links
  // Collapsible sections
  // Mobile responsiveness
};
```

## Component Patterns

### 1. Composition Pattern

Components are designed to be composable, allowing for flexible UI construction:

```typescript
// Example of composition
<Card>
  <CardHeader>
    <CardTitle>User Profile</CardTitle>
    <CardDescription>Manage your account</CardDescription>
  </CardHeader>
  <CardContent>
    <ProfileForm user={user} />
  </CardContent>
  <CardFooter>
    <Button>Save Changes</Button>
  </CardFooter>
</Card>
```

### 2. Render Props Pattern

Used for components that need to share logic but have different rendering requirements:

```typescript
<DataFetcher
  url="/api/users"
  render={(data, loading, error) => {
    if (loading) return <Spinner />;
    if (error) return <ErrorMessage error={error} />;
    return <UserList users={data} />;
  }}
/>
```

### 3. Compound Components Pattern

Used for components that have multiple related parts:

```typescript
<Tabs defaultValue="profile">
  <TabsList>
    <TabsTrigger value="profile">Profile</TabsTrigger>
    <TabsTrigger value="settings">Settings</TabsTrigger>
  </TabsList>
  <TabsContent value="profile">
    <ProfileForm />
  </TabsContent>
  <TabsContent value="settings">
    <SettingsForm />
  </TabsContent>
</Tabs>
```

## Form Handling

### 1. React Hook Form Integration

```typescript
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';

const formSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

type FormValues = z.infer<typeof formSchema>;

const LoginForm = () => {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = (values: FormValues) => {
    // Handle form submission
  };

  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      {/* Form fields */}
    </form>
  );
};
```

## Performance Optimization

### 1. Code Splitting

- Lazy load components when appropriate
- Use dynamic imports for large component trees
- Implement route-based code splitting

### 2. Memoization

- Use React.memo for expensive renders
- Implement useMemo and useCallback hooks
- Avoid unnecessary re-renders

### 3. Virtual Lists

- Implement virtualization for long lists
- Only render visible items in the viewport
- Use libraries like react-window or react-virtualized

## Accessibility

### 1. ARIA Attributes

- Proper use of aria-* attributes
- Semantic HTML elements
- Focus management

### 2. Keyboard Navigation

- Tab order management
- Keyboard shortcuts
- Focus trapping for modals

### 3. Color Contrast

- Meet WCAG 2.1 AA standards
- High contrast mode support
- Text resizing support

## Theming and Styling

### 1. Theme Configuration

- Light and dark mode support
- Custom color palette
- Typography system

### 2. CSS-in-JS with Tailwind

- Consistent styling approach
- Component-specific styles
- Responsive design utilities

## Testing Strategy

### 1. Component Testing

- Unit tests for individual components
- Integration tests for component interactions
- Snapshot testing for UI regression

### 2. Accessibility Testing

- Automated a11y testing
- Manual keyboard navigation testing
- Screen reader compatibility

## Documentation

### 1. Component Documentation

- Props documentation
- Usage examples
- Edge cases and limitations

### 2. Storybook Integration

- Interactive component examples
- Variant showcases
- Responsive design testing
