# Component Library

## Overview

This document provides an overview of the component library used in the project. The project uses [shadcn/ui](https://ui.shadcn.com/) as the primary UI component library, which is built on top of [Radix UI](https://www.radix-ui.com/) and styled with [Tailwind CSS](https://tailwindcss.com/).

## shadcn/ui Components

shadcn/ui is a collection of reusable components that you can copy and paste into your apps. The components are built using Radix UI and Tailwind CSS.

### Core Components

#### Layout Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `Container` | A centered container with responsive padding | Layout wrapper |
| `Section` | A section with responsive padding | Page sections |
| `Grid` | A responsive grid layout | Multi-column layouts |
| `Flex` | A flexible box layout | Alignment and distribution |
| `Box` | A basic layout component | Simple container |

#### Navigation Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `NavigationMenu` | A responsive navigation menu | Main navigation |
| `Breadcrumb` | A breadcrumb navigation | Page hierarchy |
| `Pagination` | A pagination component | Paginated content |
| `Tabs` | A tabbed interface | Content organization |
| `Sidebar` | A sidebar navigation | Application navigation |

#### Form Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `Form` | A form component with validation | Data collection |
| `Input` | A text input field | Text entry |
| `Textarea` | A multi-line text input | Long-form text |
| `Select` | A dropdown select | Option selection |
| `Combobox` | A searchable dropdown | Advanced selection |
| `Checkbox` | A checkbox input | Boolean selection |
| `RadioGroup` | A group of radio buttons | Exclusive selection |
| `Switch` | A toggle switch | Boolean toggle |
| `Slider` | A range slider | Numeric range selection |
| `DatePicker` | A date picker | Date selection |
| `TimePicker` | A time picker | Time selection |
| `ColorPicker` | A color picker | Color selection |
| `FileUpload` | A file upload component | File selection |

#### Feedback Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `Alert` | An alert message | User notifications |
| `Toast` | A toast notification | Temporary notifications |
| `Dialog` | A modal dialog | User interactions |
| `Drawer` | A sliding panel | Secondary content |
| `Popover` | A popover component | Contextual information |
| `Tooltip` | A tooltip component | Additional information |
| `Progress` | A progress indicator | Operation progress |
| `Skeleton` | A loading skeleton | Content placeholders |
| `Spinner` | A loading spinner | Loading indicator |

#### Data Display Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `Table` | A data table | Tabular data |
| `Card` | A card component | Content containers |
| `Avatar` | A user avatar | User representation |
| `Badge` | A badge component | Status indicators |
| `Tag` | A tag component | Categorization |
| `List` | A list component | Sequential content |
| `Accordion` | An accordion component | Collapsible content |
| `Carousel` | A carousel component | Rotating content |
| `Calendar` | A calendar component | Date display |
| `Chart` | Various chart components | Data visualization |

#### Button Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `Button` | A button component | User actions |
| `IconButton` | A button with an icon | Compact actions |
| `ButtonGroup` | A group of buttons | Related actions |
| `DropdownMenu` | A dropdown menu | Multiple options |
| `MenuButton` | A button that opens a menu | Contextual actions |
| `SplitButton` | A button with a dropdown | Primary/secondary actions |

#### Typography Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `Heading` | A heading component | Section titles |
| `Text` | A text component | Body text |
| `Paragraph` | A paragraph component | Text blocks |
| `Code` | A code component | Code snippets |
| `Blockquote` | A blockquote component | Quoted content |
| `Label` | A label component | Form labels |

### Custom Components

In addition to the shadcn/ui components, the project includes several custom components:

#### Authentication Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `LoginForm` | A login form | User authentication |
| `RegisterForm` | A registration form | User registration |
| `ForgotPasswordForm` | A password reset form | Password recovery |
| `TwoFactorForm` | A two-factor authentication form | 2FA verification |
| `OAuthButtons` | OAuth provider buttons | Social login |

#### Dashboard Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `DashboardLayout` | A dashboard layout | Admin dashboard |
| `DashboardHeader` | A dashboard header | Dashboard navigation |
| `DashboardSidebar` | A dashboard sidebar | Dashboard navigation |
| `DashboardFooter` | A dashboard footer | Dashboard information |
| `DashboardCard` | A dashboard card | Dashboard widgets |
| `DashboardStats` | Dashboard statistics | Data overview |
| `DashboardChart` | Dashboard charts | Data visualization |
| `DashboardTable` | Dashboard tables | Tabular data |

#### User Management Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `UserList` | A user list | User management |
| `UserForm` | A user form | User creation/editing |
| `UserProfile` | A user profile | User information |
| `UserAvatar` | A user avatar | User representation |
| `UserRoles` | User role management | Role assignment |
| `UserPermissions` | User permission management | Permission assignment |

#### Role Management Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `RoleList` | A role list | Role management |
| `RoleForm` | A role form | Role creation/editing |
| `RolePermissions` | Role permission management | Permission assignment |
| `RoleUsers` | Role user management | User assignment |

#### Application Management Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `ApplicationList` | An application list | Application management |
| `ApplicationForm` | An application form | Application creation/editing |
| `ApplicationRoles` | Application role management | Role assignment |
| `ApplicationUsers` | Application user management | User assignment |

#### Menu Management Components

| Component | Description | Usage |
|-----------|-------------|-------|
| `MenuList` | A menu list | Menu management |
| `MenuForm` | A menu form | Menu creation/editing |
| `MenuTree` | A menu tree | Menu hierarchy |
| `MenuItems` | Menu item management | Item assignment |

## Component Usage

### Basic Usage

```tsx
import { Button } from "@/components/ui/button";

export default function MyComponent() {
  return (
    <Button variant="default">Click Me</Button>
  );
}
```

### Form Usage

```tsx
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const formSchema = z.object({
  username: z.string().min(2).max(50),
});

export function ProfileForm() {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: "",
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    // Do something with the form values
    console.log(values);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="username"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Username</FormLabel>
              <FormControl>
                <Input placeholder="shadcn" {...field} />
              </FormControl>
              <FormDescription>
                This is your public display name.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit">Submit</Button>
      </form>
    </Form>
  );
}
```

### Table Usage

```tsx
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export function UsersTable() {
  return (
    <Table>
      <TableCaption>A list of users.</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Email</TableHead>
          <TableHead>Role</TableHead>
          <TableHead>Status</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        <TableRow>
          <TableCell>John Doe</TableCell>
          <TableCell><EMAIL></TableCell>
          <TableCell>Admin</TableCell>
          <TableCell>Active</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Jane Smith</TableCell>
          <TableCell><EMAIL></TableCell>
          <TableCell>User</TableCell>
          <TableCell>Active</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );
}
```

## Component Customization

### Theming

The components can be customized using Tailwind CSS classes and the `cn` utility:

```tsx
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

export function CustomButton({ className, ...props }) {
  return (
    <Button
      className={cn("bg-brand-500 hover:bg-brand-600", className)}
      {...props}
    />
  );
}
```

### Variants

Many components support variants that can be used to change their appearance:

```tsx
<Button variant="default">Default</Button>
<Button variant="destructive">Destructive</Button>
<Button variant="outline">Outline</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="link">Link</Button>
```

### Sizes

Some components support different sizes:

```tsx
<Button size="default">Default</Button>
<Button size="sm">Small</Button>
<Button size="lg">Large</Button>
```

## Accessibility

All components are built with accessibility in mind:

- Proper ARIA attributes
- Keyboard navigation
- Focus management
- Screen reader support
- Color contrast

## Best Practices

### Component Organization

- Keep related components together
- Use a consistent naming convention
- Document component props and usage
- Write unit tests for components

### Performance

- Use React.memo for expensive components
- Avoid unnecessary re-renders
- Optimize image loading
- Use code splitting for large components

### Maintainability

- Keep components small and focused
- Use TypeScript for type safety
- Document complex components
- Follow a consistent coding style

## Resources

- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [Radix UI Documentation](https://www.radix-ui.com/)
- [Tailwind CSS Documentation](https://tailwindcss.com/)
- [React Hook Form Documentation](https://react-hook-form.com/)
- [Zod Documentation](https://zod.dev/)
