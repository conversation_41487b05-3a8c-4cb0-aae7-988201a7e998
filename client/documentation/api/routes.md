# Routing System

## 1. Overview & Objectives

The routing system defines the application's URL structure and navigation flow, implementing Next.js 15+ App Router architecture with route groups and dynamic segments.

### Primary Goals
- Implement logical URL structure
- Ensure SEO-friendly routes
- Maintain secure access control
- Support dynamic and static routes
- Enable efficient client-side navigation

## 2. Scope & Constraints

### In Scope
- App router implementation
- Route group organization
- Dynamic route handling
- Loading and error states
- Parallel route handling

### Constraints
- Must use Next.js 15+ App Router
- No legacy pages directory
- SEO requirements for public routes
- Edge runtime compatibility
- TypeScript strict mode compliance

## 3. Functional Requirements

### Route Structure
```typescript
// Route Groups
(marketing)     // Public marketing pages
(dashboard)     // Protected dashboard routes
(auth)          // Authentication flows
(api)           // API endpoints

// Dynamic Routes
[id]            // Dynamic ID segments
[slug]          // SEO-friendly URLs
[[...params]]   // Optional catch-all routes
```

### Access Control
```typescript
const protectedRoutes = {
  dashboard: '/dashboard',
  settings: '/settings',
  profile: '/profile',
  admin: '/admin',
};

const publicRoutes = {
  home: '/',
  about: '/about',
  contact: '/contact',
  blog: '/blog',
};
```

## 4. Technical Requirements & Architecture

### Technology Stack
- Next.js App Router
- React Server Components
- Dynamic imports
- Parallel Routes
- Intercepting Routes

### Route Handlers
```typescript
// API Route Handler Example
export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  return Response.json({ data: searchParams });
}
```

## 5. Implementation Examples

### App Directory Structure

```
app/
├── (auth)/
│   ├── login/
│   │   └── page.tsx
│   ├── register/
│   │   └── page.tsx
│   ├── reset-password/
│   │   └── page.tsx
│   └── layout.tsx
├── (dashboard)/
│   ├── dashboard/
│   │   └── page.tsx
│   ├── settings/
│   │   └── page.tsx
│   ├── profile/
│   │   └── page.tsx
│   └── layout.tsx
├── (marketing)/
│   ├── page.tsx
│   ├── about/
│   │   └── page.tsx
│   ├── contact/
│   │   └── page.tsx
│   ├── blog/
│   │   ├── page.tsx
│   │   └── [slug]/
│   │       └── page.tsx
│   └── layout.tsx
├── api/
│   ├── auth/
│   │   └── [...nextauth]/
│   │       └── route.ts
│   └── users/
│       ├── route.ts
│       └── [id]/
│           └── route.ts
├── layout.tsx
├── not-found.tsx
└── error.tsx
```

### Dynamic Route Example

```typescript
// app/(marketing)/blog/[slug]/page.tsx
import { notFound } from 'next/navigation';
import { getBlogPost } from '@/lib/blog';

export async function generateMetadata({ params }: { params: { slug: string } }) {
  const post = await getBlogPost(params.slug);
  
  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }
  
  return {
    title: post.title,
    description: post.excerpt,
    openGraph: {
      title: post.title,
      description: post.excerpt,
      images: [{ url: post.featuredImage }],
    },
  };
}

export async function generateStaticParams() {
  const posts = await getBlogPosts();
  
  return posts.map((post) => ({
    slug: post.slug,
  }));
}

export default async function BlogPostPage({ params }: { params: { slug: string } }) {
  const post = await getBlogPost(params.slug);
  
  if (!post) {
    notFound();
  }
  
  return (
    <article>
      <h1>{post.title}</h1>
      <div dangerouslySetInnerHTML={{ __html: post.content }} />
    </article>
  );
}
```

### Protected Route Example

```typescript
// app/(dashboard)/layout.tsx
import { redirect } from 'next/navigation';
import { getSession } from '@/lib/auth';

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await getSession();
  
  if (!session) {
    redirect('/login');
  }
  
  return (
    <div className="dashboard-layout">
      <nav className="dashboard-nav">
        {/* Dashboard navigation */}
      </nav>
      <main className="dashboard-content">
        {children}
      </main>
    </div>
  );
}
```

## 6. Performance & Scalability Expectations

### Loading Performance
- Initial page load: < 1s
- Client-side navigation: < 100ms
- Route prefetching enabled
- Streaming support for large pages

### Caching Strategy
- Static routes: ISR with 1-hour revalidation
- Dynamic routes: On-demand revalidation
- API routes: Cache-Control headers

## 7. Error Handling & Security

### Error Boundaries
```typescript
// error.tsx for each route group
export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div>
      <h2>Something went wrong!</h2>
      <button onClick={() => reset()}>Try again</button>
    </div>
  )
}
```

### Loading States

```typescript
// loading.tsx
export default function Loading() {
  return (
    <div className="loading-container">
      <div className="loading-spinner" />
      <p>Loading...</p>
    </div>
  );
}
```

### Not Found Page

```typescript
// not-found.tsx
import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="not-found">
      <h1>404 - Page Not Found</h1>
      <p>The page you are looking for does not exist.</p>
      <Link href="/">
        Return to Home
      </Link>
    </div>
  );
}
```

### Security Measures
- Route-based authentication
- CSRF protection
- Rate limiting per route
- Input validation
- XSS prevention

## 8. Testing & Acceptance Criteria

### Unit Tests
- Route matching patterns
- Parameter extraction
- Guard clause behavior
- Loading states
- Error boundaries

### Integration Tests
- Navigation flows
- Authentication redirects
- Dynamic parameters
- Loading states
- Error handling

### Acceptance Criteria
- All routes follow naming conventions
- Protected routes require authentication
- Error pages display correctly
- Loading states implemented
- SEO requirements met
- TypeScript types strict

## 9. SEO Considerations

### Metadata API

```typescript
// app/(marketing)/page.tsx
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Home | My Application',
  description: 'Welcome to my application',
  openGraph: {
    title: 'Home | My Application',
    description: 'Welcome to my application',
    url: 'https://myapp.com',
    siteName: 'My Application',
    images: [
      {
        url: 'https://myapp.com/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'My Application',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
};

export default function HomePage() {
  return (
    <div>
      <h1>Welcome to My Application</h1>
      {/* Page content */}
    </div>
  );
}
```

### Sitemap Generation

```typescript
// app/sitemap.ts
import { MetadataRoute } from 'next';
import { getBlogPosts } from '@/lib/blog';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://myapp.com';
  const posts = await getBlogPosts();
  
  const blogUrls = posts.map((post) => ({
    url: `${baseUrl}/blog/${post.slug}`,
    lastModified: post.updatedAt,
    changeFrequency: 'weekly',
    priority: 0.7,
  }));
  
  return [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'yearly',
      priority: 1,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/blog`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    ...blogUrls,
  ];
}
```

## 10. Best Practices

- Use route groups for logical organization
- Implement proper loading and error states
- Leverage static generation for performance
- Ensure proper SEO metadata
- Implement proper access control
- Use TypeScript for type safety
- Follow Next.js naming conventions
