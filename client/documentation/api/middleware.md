# Middleware System

## 1. Overview & Objectives

The middleware system serves as a critical request processing layer that handles authentication, authorization, and security measures. It intercepts and processes all incoming requests before they reach the route handlers.

### Primary Goals
- Ensure secure request processing
- Manage authentication flows
- Implement rate limiting
- Apply security headers
- Handle request routing logic

## 2. Scope & Constraints

### In Scope
- Request authentication validation
- Rate limiting implementation
- Security header management
- Route type classification
- Redirect handling

### Constraints
- Must be compatible with Next.js 15+ runtime
- Must support Edge Runtime
- Memory usage optimization for rate limiting
- Response time under 100ms

## 3. Functional Requirements

### Route Classification
```typescript
const publicRoutePaths: string[] = ['/', '/api', '/auth/email-verification', '/test', '/promote'];
const authRoutePaths: string[] = [
  '/auth/login',
  '/auth/register',
  '/auth/new-password',
  '/auth/reset-password',
  '/auth/error',
];
```

### Rate Limiting
```typescript
const RATE_LIMIT_DURATION = 60 * 1000; // 1 minute
const MAX_REQUESTS = 100; // Maximum requests per minute
```

## 4. Technical Requirements & Architecture

### Technology Stack
- Next.js Middleware API
- Edge Runtime
- In-memory rate limiting
- JWT token validation

### Security Headers
```typescript
response.headers.set('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload');
```

### Request Processing Flow
1. Rate limit validation
2. Security headers injection
3. Route type determination
4. Authentication status check
5. Redirect processing

## 5. Implementation Example

```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

// Route classifications
const publicRoutePaths: string[] = ['/', '/api', '/auth/email-verification', '/test', '/promote'];
const authRoutePaths: string[] = [
  '/auth/login',
  '/auth/register',
  '/auth/new-password',
  '/auth/reset-password',
  '/auth/error',
];

// Rate limiting configuration
const RATE_LIMIT_DURATION = 60 * 1000; // 1 minute
const MAX_REQUESTS = 100; // Maximum requests per minute

// In-memory store for rate limiting
const rateLimitStore = new Map<string, { count: number; timestamp: number }>();

// Clean up the rate limit store periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, value] of rateLimitStore.entries()) {
    if (now - value.timestamp > RATE_LIMIT_DURATION) {
      rateLimitStore.delete(key);
    }
  }
}, 5 * 60 * 1000); // Clean up every 5 minutes

export async function middleware(request: NextRequest) {
  const response = NextResponse.next();
  
  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Strict-Transport-Security', 'max-age=63072000; includeSubDomains; preload');
  
  // Apply rate limiting
  const ip = request.ip || 'unknown';
  const rateLimit = rateLimitStore.get(ip) || { count: 0, timestamp: Date.now() };
  
  // Reset count if outside the window
  if (Date.now() - rateLimit.timestamp > RATE_LIMIT_DURATION) {
    rateLimit.count = 0;
    rateLimit.timestamp = Date.now();
  }
  
  rateLimit.count++;
  rateLimitStore.set(ip, rateLimit);
  
  // Check if rate limit exceeded
  if (rateLimit.count > MAX_REQUESTS) {
    return new NextResponse(JSON.stringify({ error: 'Too Many Requests' }), {
      status: 429,
      headers: { 
        'Content-Type': 'application/json',
        'Retry-After': '60'
      },
    });
  }
  
  // Get the pathname
  const { pathname } = request.nextUrl;
  
  // Check if the route is public
  const isPublicRoute = publicRoutePaths.some(path => pathname.startsWith(path));
  
  // Check if the route is an auth route
  const isAuthRoute = authRoutePaths.some(path => pathname === path);
  
  // If the route is public, allow access
  if (isPublicRoute) {
    return response;
  }
  
  // Get the token
  const token = await getToken({ req: request });
  
  // If the user is authenticated and trying to access an auth route, redirect to dashboard
  if (token && isAuthRoute) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }
  
  // If the user is not authenticated and trying to access a protected route, redirect to login
  if (!token && !isAuthRoute) {
    return NextResponse.redirect(new URL('/auth/login', request.url));
  }
  
  return response;
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};
```

## 6. Performance & Scalability Expectations

### Response Time
- Maximum middleware processing time: 100ms
- Rate limiting lookup: < 10ms
- Authentication check: < 50ms

### Memory Usage
- Rate limiting storage cleanup every 5 minutes
- Maximum memory usage: 100MB
- Sliding window algorithm for rate limiting

## 7. Error Handling & Security

### Rate Limiting Errors
```typescript
return new NextResponse(JSON.stringify({ error: 'Too Many Requests' }), {
  status: 429,
  headers: { 
    'Content-Type': 'application/json',
    'Retry-After': '60'
  },
});
```

### Security Measures
- HSTS implementation
- Rate limiting by IP
- Authentication token validation
- XSS protection headers

## 8. Testing & Acceptance Criteria

### Unit Tests
- Route matching logic
- Rate limiting functionality
- Authentication flow
- Header injection

### Integration Tests
- Complete request pipeline
- Error handling scenarios
- Rate limiting behavior
- Authentication flows

### Acceptance Criteria
- All public routes accessible without authentication
- Protected routes properly secured
- Rate limiting functioning as specified
- Security headers present in all responses
- Redirect logic working correctly

## 9. Deployment Considerations

### Edge Runtime
- Middleware is deployed to the Edge Runtime
- Minimal latency impact on requests
- Global distribution for optimal performance

### Monitoring
- Track rate limiting metrics
- Monitor authentication failures
- Measure middleware response time
- Alert on excessive rate limiting events

## 10. Future Enhancements

- Implement more sophisticated rate limiting algorithms
- Add support for different rate limits based on route or user role
- Integrate with external rate limiting services
- Enhance security headers based on OWASP recommendations
- Add support for custom error pages
