# API Specification

## 1. System Overview

This document details the API operations implementation specifications, including user management, authentication operations, and other functionalities. API operations are implemented using the Server Actions pattern, ensuring type safety and performance optimization.

## 2. User Management Operations

### 2.1 Retrieving User Information

#### getUserByEmail

```typescript
async function getUserByEmail(
  email: string,
  includeLoginMethods = false
): Promise<UserWithLoginMethods | null>;
```

- **Purpose**: Query user information by email address
- **Parameters**:
  - email: User's email address
  - includeLoginMethods: Whether to include login method information
- **Returns**: User information object or null
- **Error Handling**:
  - Database connection errors
  - Query execution errors

#### getUserById

```typescript
async function getUserById(
  id: string,
  includeLoginMethods = false
): Promise<UserWithLoginMethods | null>;
```

- **Purpose**: Query user information by user ID
- **Parameters**:
  - id: User's unique identifier
  - includeLoginMethods: Whether to include login method information
- **Returns**: User information object or null
- **Error Handling**:
  - Database connection errors
  - Query execution errors

### 2.2 User Registration

```typescript
async function registerUser(data: {
  name: string;
  email: string;
  password: string;
}): Promise<UserWithLoginMethods>;
```

- **Purpose**: Register a new user
- **Parameters**:
  - name: User's name
  - email: Email address
  - password: Password
- **Returns**: Created user object
- **Additional Operations**:
  - Create password login method record
  - Log activity
- **Error Handling**:
  - Duplicate email
  - Data validation errors

### 2.3 Updating User Information

```typescript
async function updateUser(
  id: string,
  data: Partial<UserWithLoginMethods>
): Promise<UserWithLoginMethods>;
```

- **Purpose**: Update user information
- **Parameters**:
  - id: User ID
  - data: User data to update
- **Returns**: Updated user object
- **Error Handling**:
  - User does not exist
  - Data validation errors

### 2.4 Deleting a User

```typescript
async function deleteUser(id: string): Promise<void>;
```

- **Purpose**: Delete a user
- **Parameters**: id: User ID
- **Additional Operations**: Cascade delete related data
- **Error Handling**:
  - User does not exist
  - Deletion operation errors

## 3. Authentication Operations

### 3.1 Login Operation

```typescript
async function login(credentials: { email: string; password: string }): Promise<AuthResult>;
```

- **Purpose**: User login
- **Parameters**:
  - email: Email address
  - password: Password
- **Returns**: Authentication result
- **Validation Flow**:
  1. Validate input data
  2. Check if user exists
  3. Validate password
  4. Check two-factor authentication
- **Error Handling**:
  - Invalid credentials
  - Account lockout
  - Two-factor authentication required

### 3.2 Logout Operation

```typescript
async function logout(): Promise<void>;
```

- **Purpose**: User logout
- **Operations**:
  1. Clear session
  2. Clear cookies
  3. Redirect to login page

## 4. Error Handling

### 4.1 Error Types

```typescript
interface ApiError {
  code: string;
  message: string;
  details?: unknown;
}
```

### 4.2 Standard Error Codes

- `DB_CONNECTION_ERROR`: Database connection error
- `USER_NOT_FOUND`: User does not exist
- `INVALID_CREDENTIALS`: Invalid credentials
- `VALIDATION_ERROR`: Data validation error
- `OPERATION_FAILED`: Operation execution failed

### 4.3 Error Response Format

```typescript
interface ErrorResponse {
  success: false;
  error: ApiError;
}
```

## 5. Logging

### 5.1 Log Levels

- ERROR: Error information
- WARN: Warning information
- INFO: Operation information
- DEBUG: Debug information

### 5.2 Log Content

- Timestamp
- Operation type
- User information
- Error details
- Stack trace (development environment only)

## 6. Performance Considerations

### 6.1 Query Optimization

- Use of indexes
- Selective loading of related data
- Paginated queries

### 6.2 Caching Strategy

- User information caching
- Query result caching
- Cache invalidation strategy

## 7. Security Considerations

### 7.1 Input Validation

- Use of Zod schema validation
- XSS protection
- SQL injection prevention

### 7.2 Authentication and Authorization

- JWT token validation
- Role-based access control
- Permission checking

### 7.3 Rate Limiting

- API request rate limiting
- Login attempt limiting
- IP-based blocking

## 8. API Endpoints

### 8.1 User Management Endpoints

- `POST /api/users`: Create a new user
- `GET /api/users/:id`: Get user by ID
- `PATCH /api/users/:id`: Update user information
- `DELETE /api/users/:id`: Delete a user

### 8.2 Authentication Endpoints

- `POST /api/auth/login`: User login
- `POST /api/auth/logout`: User logout
- `POST /api/auth/register`: User registration
- `POST /api/auth/reset-password`: Password reset

## 9. Response Formats

### 9.1 Success Response

```typescript
interface SuccessResponse<T> {
  success: true;
  data: T;
}
```

### 9.2 Error Response

```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: unknown;
  };
}
```

## 10. API Versioning

- Version included in URL path: `/api/v1/users`
- Version included in request header: `API-Version: 1`
- Backward compatibility considerations
- Deprecation policy and notification

## 11. API Endpoints

### Authentication

#### POST /api/auth/login

Authenticates a user and returns a JWT token.

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (200 OK):**
```json
{
  "user": {
    "id": "user_id",
    "name": "User Name",
    "email": "<EMAIL>",
    "image": "https://example.com/avatar.jpg",
    "role": "user"
  },
  "token": "jwt_token"
}
```

**Response (401 Unauthorized):**
```json
{
  "error": "Invalid credentials"
}
```

#### POST /api/auth/register

Registers a new user.

**Request:**
```json
{
  "name": "New User",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response (201 Created):**
```json
{
  "user": {
    "id": "user_id",
    "name": "New User",
    "email": "<EMAIL>"
  },
  "message": "User registered successfully"
}
```

**Response (400 Bad Request):**
```json
{
  "error": "Email already in use"
}
```

#### POST /api/auth/reset-password

Initiates a password reset.

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

**Response (200 OK):**
```json
{
  "message": "Password reset email sent"
}
```

#### POST /api/auth/verify-email

Verifies a user's email address.

**Request:**
```json
{
  "token": "verification_token"
}
```

**Response (200 OK):**
```json
{
  "message": "Email verified successfully"
}
```

**Response (400 Bad Request):**
```json
{
  "error": "Invalid or expired token"
}
```

#### POST /api/auth/two-factor

Verifies a two-factor authentication code.

**Request:**
```json
{
  "userId": "user_id",
  "code": "123456"
}
```

**Response (200 OK):**
```json
{
  "verified": true
}
```

**Response (400 Bad Request):**
```json
{
  "error": "Invalid or expired code"
}
```

### User Management

#### GET /api/users/me

Gets the current user's information.

**Response (200 OK):**
```json
{
  "id": "user_id",
  "name": "User Name",
  "email": "<EMAIL>",
  "image": "https://example.com/avatar.jpg",
  "role": "user",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

#### GET /api/users/:id

Gets a user by ID.

**Response (200 OK):**
```json
{
  "id": "user_id",
  "name": "User Name",
  "email": "<EMAIL>",
  "image": "https://example.com/avatar.jpg",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z"
}
```

**Response (404 Not Found):**
```json
{
  "error": "User not found"
}
```

#### PATCH /api/users/:id

Updates a user's information.

**Request:**
```json
{
  "name": "Updated Name",
  "image": "https://example.com/new-avatar.jpg"
}
```

**Response (200 OK):**
```json
{
  "id": "user_id",
  "name": "Updated Name",
  "email": "<EMAIL>",
  "image": "https://example.com/new-avatar.jpg",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

#### DELETE /api/users/:id

Deletes a user.

**Response (200 OK):**
```json
{
  "message": "User deleted successfully"
}
```

### Role Management

#### GET /api/roles

Gets all roles.

**Response (200 OK):**
```json
{
  "roles": [
    {
      "id": "role_id",
      "name": "admin",
      "description": "Administrator role",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    },
    {
      "id": "role_id",
      "name": "user",
      "description": "Regular user role",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

#### POST /api/roles

Creates a new role.

**Request:**
```json
{
  "name": "editor",
  "description": "Editor role"
}
```

**Response (201 Created):**
```json
{
  "id": "role_id",
  "name": "editor",
  "description": "Editor role",
  "createdAt": "2023-01-02T00:00:00.000Z",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

#### GET /api/roles/:id

Gets a role by ID.

**Response (200 OK):**
```json
{
  "id": "role_id",
  "name": "admin",
  "description": "Administrator role",
  "createdAt": "2023-01-01T00:00:00.000Z",
  "updatedAt": "2023-01-01T00:00:00.000Z",
  "permissions": [
    {
      "id": "permission_id",
      "name": "user:create",
      "description": "Create users"
    },
    {
      "id": "permission_id",
      "name": "user:delete",
      "description": "Delete users"
    }
  ]
}
```

#### PATCH /api/roles/:id

Updates a role.

**Request:**
```json
{
  "description": "Updated description"
}
```

**Response (200 OK):**
```json
{
  "id": "role_id",
  "name": "admin",
  "description": "Updated description",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

#### DELETE /api/roles/:id

Deletes a role.

**Response (200 OK):**
```json
{
  "message": "Role deleted successfully"
}
```

### Permission Management

#### GET /api/permissions

Gets all permissions.

**Response (200 OK):**
```json
{
  "permissions": [
    {
      "id": "permission_id",
      "name": "user:create",
      "description": "Create users",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    },
    {
      "id": "permission_id",
      "name": "user:delete",
      "description": "Delete users",
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

#### POST /api/permissions

Creates a new permission.

**Request:**
```json
{
  "name": "user:update",
  "description": "Update users"
}
```

**Response (201 Created):**
```json
{
  "id": "permission_id",
  "name": "user:update",
  "description": "Update users",
  "createdAt": "2023-01-02T00:00:00.000Z",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

### Application Management

#### GET /api/applications

Gets all applications.

**Response (200 OK):**
```json
{
  "applications": [
    {
      "id": "app_id",
      "name": "dashboard",
      "displayName": "Dashboard",
      "description": "Main dashboard application",
      "path": "dashboard",
      "icon": "dashboard",
      "isActive": true,
      "order": 1,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    },
    {
      "id": "app_id",
      "name": "users",
      "displayName": "User Management",
      "description": "User management application",
      "path": "users",
      "icon": "users",
      "isActive": true,
      "order": 2,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

#### POST /api/applications

Creates a new application.

**Request:**
```json
{
  "name": "settings",
  "displayName": "Settings",
  "description": "Application settings",
  "path": "settings",
  "icon": "settings",
  "order": 3
}
```

**Response (201 Created):**
```json
{
  "id": "app_id",
  "name": "settings",
  "displayName": "Settings",
  "description": "Application settings",
  "path": "settings",
  "icon": "settings",
  "isActive": true,
  "order": 3,
  "createdAt": "2023-01-02T00:00:00.000Z",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

### Menu Management

#### GET /api/menu-items

Gets all menu items.

**Response (200 OK):**
```json
{
  "menuItems": [
    {
      "id": "menu_id",
      "name": "dashboard",
      "displayName": "Dashboard",
      "path": "/dashboard",
      "icon": "dashboard",
      "parentId": null,
      "applicationId": "app_id",
      "order": 1,
      "isVisible": true,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    },
    {
      "id": "menu_id",
      "name": "users",
      "displayName": "Users",
      "path": "/users",
      "icon": "users",
      "parentId": null,
      "applicationId": "app_id",
      "order": 2,
      "isVisible": true,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ]
}
```

#### POST /api/menu-items

Creates a new menu item.

**Request:**
```json
{
  "name": "settings",
  "displayName": "Settings",
  "path": "/settings",
  "icon": "settings",
  "applicationId": "app_id",
  "order": 3
}
```

**Response (201 Created):**
```json
{
  "id": "menu_id",
  "name": "settings",
  "displayName": "Settings",
  "path": "/settings",
  "icon": "settings",
  "parentId": null,
  "applicationId": "app_id",
  "order": 3,
  "isVisible": true,
  "createdAt": "2023-01-02T00:00:00.000Z",
  "updatedAt": "2023-01-02T00:00:00.000Z"
}
```

## Error Handling

### Error Response Format

All API errors follow a consistent format:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "Error details for specific field"
  }
}
```

### HTTP Status Codes

- `200 OK`: Request succeeded
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict
- `422 Unprocessable Entity`: Validation error
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

### Error Codes

- `VALIDATION_ERROR`: Request validation failed
- `AUTHENTICATION_ERROR`: Authentication failed
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `RESOURCE_NOT_FOUND`: Resource not found
- `RESOURCE_CONFLICT`: Resource conflict
- `RATE_LIMIT_EXCEEDED`: Rate limit exceeded
- `INTERNAL_SERVER_ERROR`: Server error

## Rate Limiting

API endpoints are rate-limited to prevent abuse. The rate limits are:

- Authentication endpoints: 10 requests per minute
- Other endpoints: 60 requests per minute

Rate limit information is included in the response headers:

```
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1609459200
```

## Pagination

List endpoints support pagination using the following query parameters:

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

Pagination information is included in the response:

```json
{
  "data": [...],
  "pagination": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "pages": 10
  }
}
```

## Filtering

List endpoints support filtering using query parameters:

```
GET /api/users?role=admin&createdAt[gte]=2023-01-01
```

## Sorting

List endpoints support sorting using the `sort` query parameter:

```
GET /api/users?sort=name:asc,createdAt:desc
```

## API Versioning

API versioning is handled through the URL path:

```
/api/v1/users
/api/v2/users
```

The current version is v1.

## CORS

The API supports Cross-Origin Resource Sharing (CORS) for specified origins.

## API Documentation

Interactive API documentation is available at `/api/docs` using Swagger UI.
