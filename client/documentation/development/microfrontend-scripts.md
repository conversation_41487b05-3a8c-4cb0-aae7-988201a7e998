# Micro-Frontend Scripts

This document explains the scripts created to manage the micro-frontend architecture in the project.

## Available Scripts

The following scripts are available in the `scripts/` directory:

### 1. comboCompile.sh

This script compiles both the main application and the micro-frontend application in one go.

**Usage:**
```bash
# From the project root
./scripts/comboCompile.sh

# To force a clean install
./scripts/comboCompile.sh --force
```

**What it does:**
- Cleans build artifacts (`.next/` directory) for both applications
- Installs dependencies for both applications (if needed)
- Generates Prisma models for the main application
- Builds both applications

### 2. startAll.sh

This script starts both the main application and the micro-frontend application in production mode.

**Usage:**
```bash
# From the project root
./scripts/startAll.sh
```

**What it does:**
- Checks if both applications are built and builds them if not
- Starts the micro-frontend on port 3001
- Starts the main application on port 3000
- Provides clean shutdown of both applications when Ctrl+C is pressed

### 3. devAll.sh

This script starts both the main application and the micro-frontend application in development mode.

**Usage:**
```bash
# From the project root
./scripts/devAll.sh
```

**What it does:**
- Starts the micro-frontend in development mode on port 3001
- Starts the main application in development mode on port 3000
- Provides clean shutdown of both applications when Ctrl+C is pressed

## Running the Micro-Frontend Architecture

To fully experience the micro-frontend architecture, follow these steps:

1. **Initial setup and build:**
   ```bash
   ./scripts/comboCompile.sh
   ```

2. **Development mode:**
   ```bash
   ./scripts/devAll.sh
   ```

3. **Production mode:**
   ```bash
   ./scripts/startAll.sh
   ```

4. Visit `http://localhost:3000/admin/applications` to see the micro-frontend in action.

## How It Works

The micro-frontend architecture uses Webpack Module Federation to expose components from one application and consume them in another. The main workflow is:

1. The micro-frontend application (`sampleApp`) defines and exports components
2. The main application imports and renders these components
3. The components are loaded at runtime, not at build time

The scripts help manage the complexity of running multiple applications simultaneously by:
- Ensuring correct startup order
- Handling dependency installation
- Managing build processes
- Providing clean shutdown

## Script Implementation Details

### comboCompile.sh

```bash
#!/bin/bash

FORCE_INSTALL=false

# Check if --force flag is provided
if [ "$1" == "--force" ]; then
  FORCE_INSTALL=true
fi

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Starting combined compilation process...${NC}"

# Clean build artifacts if force flag is set
if [ "$FORCE_INSTALL" = true ]; then
  echo -e "${YELLOW}Force flag detected. Cleaning build artifacts...${NC}"
  rm -rf ./apps/main/.next
  rm -rf ./apps/sample-app/.next
  rm -rf ./node_modules
  rm -rf ./apps/main/node_modules
  rm -rf ./apps/sample-app/node_modules
fi

# Install root dependencies
echo -e "${GREEN}Installing root dependencies...${NC}"
yarn install

# Generate Prisma client
echo -e "${GREEN}Generating Prisma client...${NC}"
cd ./apps/main && npx prisma generate

# Build sample app
echo -e "${GREEN}Building sample micro-frontend app...${NC}"
cd ../sample-app && yarn build

# Build main app
echo -e "${GREEN}Building main application...${NC}"
cd ../main && yarn build

echo -e "${GREEN}Compilation complete!${NC}"
```

### startAll.sh

```bash
#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if apps are built
if [ ! -d "./apps/main/.next" ] || [ ! -d "./apps/sample-app/.next" ]; then
  echo -e "${YELLOW}One or more apps are not built. Running compilation first...${NC}"
  ./scripts/comboCompile.sh
fi

# Function to handle clean shutdown
function cleanup() {
  echo -e "${YELLOW}Shutting down all applications...${NC}"
  kill $SAMPLE_PID $MAIN_PID 2>/dev/null
  exit 0
}

# Set up trap for clean shutdown
trap cleanup SIGINT SIGTERM

# Start sample app
echo -e "${GREEN}Starting sample micro-frontend on port 3001...${NC}"
cd ./apps/sample-app && yarn start &
SAMPLE_PID=$!

# Wait a moment for the sample app to initialize
sleep 2

# Start main app
echo -e "${GREEN}Starting main application on port 3000...${NC}"
cd ../main && yarn start &
MAIN_PID=$!

echo -e "${GREEN}All applications started. Press Ctrl+C to stop all.${NC}"
echo -e "${GREEN}Main app: http://localhost:3000${NC}"
echo -e "${GREEN}Sample app: http://localhost:3001${NC}"

# Wait for both processes
wait $SAMPLE_PID $MAIN_PID
```

### devAll.sh

```bash
#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to handle clean shutdown
function cleanup() {
  echo -e "${YELLOW}Shutting down all applications...${NC}"
  kill $SAMPLE_PID $MAIN_PID 2>/dev/null
  exit 0
}

# Set up trap for clean shutdown
trap cleanup SIGINT SIGTERM

# Start sample app in dev mode
echo -e "${GREEN}Starting sample micro-frontend in development mode on port 3001...${NC}"
cd ./apps/sample-app && yarn dev &
SAMPLE_PID=$!

# Wait a moment for the sample app to initialize
sleep 2

# Start main app in dev mode
echo -e "${GREEN}Starting main application in development mode on port 3000...${NC}"
cd ../main && yarn dev &
MAIN_PID=$!

echo -e "${GREEN}All applications started in development mode. Press Ctrl+C to stop all.${NC}"
echo -e "${GREEN}Main app: http://localhost:3000${NC}"
echo -e "${GREEN}Sample app: http://localhost:3001${NC}"

# Wait for both processes
wait $SAMPLE_PID $MAIN_PID
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: If ports 3000 or 3001 are already in use, the scripts will fail. Make sure these ports are available before running the scripts.

2. **Build failures**: If the build process fails, check the error messages and fix any issues in the respective application.

3. **Module Federation errors**: If you see errors related to Module Federation, ensure that the remote entry URLs are correctly configured in the `.env` file.

### Debugging Tips

1. Run each application separately to isolate issues:
   ```bash
   # For the sample app
   cd ./apps/sample-app && yarn dev
   
   # For the main app
   cd ./apps/main && yarn dev
   ```

2. Check the browser console for any JavaScript errors related to Module Federation.

3. Verify that the remote entry file is accessible by navigating to:
   ```
   http://localhost:3001/_next/static/chunks/remoteEntry.js
   ```
