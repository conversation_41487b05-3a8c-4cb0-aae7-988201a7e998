# Testing Strategy

## Overview
This document outlines the testing strategy and implementation details for the application, covering unit tests, integration tests, and end-to-end tests.

## Testing Architecture

### 1. Directory Structure
```
tests/
├── unit/
│   ├── components/
│   ├── hooks/
│   └── utils/
├── integration/
│   ├── api/
│   └── auth/
└── e2e/
    ├── auth/
    └── features/
```

## Testing Frameworks

### 1. Unit Testing
- Jest for test runner
- React Testing Library for component testing
- MSW for API mocking

### 2. Integration Testing
- Jest for test environment
- Supertest for API testing
- Test database for data layer

### 3. E2E Testing
- Playwright for browser automation
- Custom fixtures and helpers
- Visual regression testing

## Test Types

### 1. Unit Tests
```typescript
// components/auth/__tests__/LoginForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { LoginForm } from '../LoginForm'
import { loginAction } from '@/actions/auth/authActions'

jest.mock('@/actions/auth/authActions')

describe('LoginForm', () => {
    beforeEach(() => {
        jest.clearAllMocks()
    })

    it('renders login form correctly', () => {
        render(<LoginForm />)
        
        expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
        expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
        expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
    })

    it('handles form submission correctly', async () => {
        const mockLoginAction = loginAction as jest.Mock
        mockLoginAction.mockResolvedValueOnce({ success: true })

        render(<LoginForm />)

        fireEvent.change(screen.getByLabelText(/email/i), {
            target: { value: '<EMAIL>' }
        })
        fireEvent.change(screen.getByLabelText(/password/i), {
            target: { value: 'password123' }
        })
        fireEvent.click(screen.getByRole('button', { name: /sign in/i }))

        await waitFor(() => {
            expect(mockLoginAction).toHaveBeenCalledWith({
                email: '<EMAIL>',
                password: 'password123'
            })
        })
    })

    it('displays error message on failed login', async () => {
        const mockLoginAction = loginAction as jest.Mock
        mockLoginAction.mockResolvedValueOnce({ error: 'Invalid credentials' })

        render(<LoginForm />)

        fireEvent.change(screen.getByLabelText(/email/i), {
            target: { value: '<EMAIL>' }
        })
        fireEvent.change(screen.getByLabelText(/password/i), {
            target: { value: 'wrongpassword' }
        })
        fireEvent.click(screen.getByRole('button', { name: /sign in/i }))

        await waitFor(() => {
            expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument()
        })
    })
})
```

### 2. Integration Tests
```typescript
// integration/auth/login.test.ts
import { createMocks } from 'node-mocks-http'
import { loginHandler } from '@/app/api/auth/login/route'
import { prisma } from '@/lib/prisma'
import { hash } from 'bcrypt'

describe('Login API', () => {
    beforeEach(async () => {
        await prisma.user.deleteMany()
    })

    it('authenticates user with valid credentials', async () => {
        const hashedPassword = await hash('password123', 12)
        await prisma.user.create({
            data: {
                email: '<EMAIL>',
                password: hashedPassword
            }
        })

        const { req, res } = createMocks({
            method: 'POST',
            body: {
                email: '<EMAIL>',
                password: 'password123'
            }
        })

        await loginHandler(req, res)

        expect(res._getStatusCode()).toBe(200)
        const jsonResponse = JSON.parse(res._getData())
        expect(jsonResponse).toHaveProperty('token')
    })

    it('returns error for invalid credentials', async () => {
        const { req, res } = createMocks({
            method: 'POST',
            body: {
                email: '<EMAIL>',
                password: 'wrongpassword'
            }
        })

        await loginHandler(req, res)

        expect(res._getStatusCode()).toBe(401)
        const jsonResponse = JSON.parse(res._getData())
        expect(jsonResponse).toHaveProperty('error', 'Invalid credentials')
    })
})
```

### 3. E2E Tests
```typescript
// e2e/auth/login.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Login Flow', () => {
    test.beforeEach(async ({ page }) => {
        await page.goto('/auth/login')
    })

    test('successful login redirects to dashboard', async ({ page }) => {
        await page.fill('input[name="email"]', '<EMAIL>')
        await page.fill('input[name="password"]', 'password123')
        await page.click('button[type="submit"]')

        await expect(page).toHaveURL('/dashboard')
        await expect(page.locator('text=Welcome back')).toBeVisible()
    })

    test('displays error for invalid credentials', async ({ page }) => {
        await page.fill('input[name="email"]', '<EMAIL>')
        await page.fill('input[name="password"]', 'wrongpassword')
        await page.click('button[type="submit"]')

        await expect(page.locator('text=Invalid credentials')).toBeVisible()
        await expect(page).toHaveURL('/auth/login')
    })

    test('validates form fields', async ({ page }) => {
        await page.click('button[type="submit"]')

        await expect(page.locator('text=Email is required')).toBeVisible()
        await expect(page.locator('text=Password is required')).toBeVisible()
    })
})
```

## Test Configuration

### 1. Jest Configuration
```javascript
// jest.config.js
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jest-environment-jsdom',
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/.next/',
    '<rootDir>/e2e/',
  ],
  collectCoverageFrom: [
    'components/**/*.{ts,tsx}',
    'lib/**/*.{ts,tsx}',
    'app/**/*.{ts,tsx}',
    '!app/api/**/*.{ts,tsx}',
  ],
  coverageThreshold: {
    global: {
      statements: 80,
      branches: 80,
      functions: 80,
      lines: 80,
    },
  },
}

module.exports = createJestConfig(customJestConfig)
```

### 2. Playwright Configuration
```javascript
// playwright.config.ts
import { PlaywrightTestConfig, devices } from '@playwright/test'

const config: PlaywrightTestConfig = {
  testDir: './e2e',
  timeout: 30000,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html', { outputFolder: 'playwright-report' }],
    ['json', { outputFile: 'playwright-report/test-results.json' }],
  ],
  use: {
    baseURL: process.env.BASE_URL || 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'mobile-chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'mobile-safari',
      use: { ...devices['iPhone 12'] },
    },
  ],
}

export default config
```

## Testing Strategies

### 1. Component Testing
- Test component rendering
- Test user interactions
- Test state changes
- Test prop variations
- Test error states
- Test accessibility

### 2. Hook Testing
```typescript
// hooks/__tests__/useAuth.test.ts
import { renderHook, act } from '@testing-library/react'
import { useAuth } from '../useAuth'
import { loginUser, logoutUser } from '@/lib/auth'

jest.mock('@/lib/auth')

describe('useAuth', () => {
  it('initializes with unauthenticated state', () => {
    const { result } = renderHook(() => useAuth())
    
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.user).toBeNull()
  })
  
  it('handles login correctly', async () => {
    const mockUser = { id: '1', email: '<EMAIL>' }
    const mockLoginUser = loginUser as jest.Mock
    mockLoginUser.mockResolvedValueOnce({ user: mockUser })
    
    const { result } = renderHook(() => useAuth())
    
    await act(async () => {
      await result.current.login({ email: '<EMAIL>', password: 'password123' })
    })
    
    expect(result.current.isAuthenticated).toBe(true)
    expect(result.current.user).toEqual(mockUser)
  })
  
  it('handles logout correctly', async () => {
    const mockLogoutUser = logoutUser as jest.Mock
    mockLogoutUser.mockResolvedValueOnce(undefined)
    
    const { result } = renderHook(() => useAuth())
    
    // Set initial authenticated state
    result.current.setUser({ id: '1', email: '<EMAIL>' })
    
    await act(async () => {
      await result.current.logout()
    })
    
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.user).toBeNull()
  })
})
```

### 3. API Testing
- Test request validation
- Test response format
- Test error handling
- Test authentication
- Test rate limiting
- Test data persistence

### 4. E2E Testing
- Test user flows
- Test form submissions
- Test navigation
- Test authentication flows
- Test responsive design
- Test accessibility

## Test Data Management

### 1. Test Database
- Use separate test database
- Reset database before tests
- Seed test data
- Use transactions for isolation

### 2. Mock Data
```typescript
// mocks/users.ts
export const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'User One',
    password: 'hashedpassword1',
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'User Two',
    password: 'hashedpassword2',
  },
]
```

### 3. API Mocking
```typescript
// mocks/handlers.ts
import { rest } from 'msw'
import { mockUsers } from './users'

export const handlers = [
  rest.post('/api/auth/login', (req, res, ctx) => {
    const { email, password } = req.body
    
    const user = mockUsers.find(u => u.email === email)
    
    if (!user) {
      return res(
        ctx.status(401),
        ctx.json({ error: 'Invalid credentials' })
      )
    }
    
    return res(
      ctx.status(200),
      ctx.json({
        user: { id: user.id, email: user.email, name: user.name },
        token: 'mock-jwt-token',
      })
    )
  }),
  
  rest.get('/api/users', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        users: mockUsers.map(u => ({ id: u.id, email: u.email, name: u.name })),
      })
    )
  }),
]
```

## CI/CD Integration

### 1. GitHub Actions
```yaml
# .github/workflows/test.yml
name: Test

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'yarn'
      
      - name: Install dependencies
        run: yarn install --frozen-lockfile
      
      - name: Setup database
        run: yarn prisma migrate deploy
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
      
      - name: Run unit and integration tests
        run: yarn test
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          JWT_SECRET: test_secret
      
      - name: Install Playwright
        run: npx playwright install --with-deps
      
      - name: Build application
        run: yarn build
      
      - name: Start application
        run: yarn start &
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          JWT_SECRET: test_secret
          NEXTAUTH_URL: http://localhost:3000
          NEXTAUTH_SECRET: test_secret
      
      - name: Run E2E tests
        run: yarn test:e2e
        env:
          BASE_URL: http://localhost:3000
```

## Code Coverage

### 1. Coverage Reporting
- Track code coverage metrics
- Set minimum coverage thresholds
- Generate coverage reports
- Integrate with CI/CD

### 2. Coverage Configuration
```javascript
// jest.config.js (coverage section)
collectCoverageFrom: [
  'components/**/*.{ts,tsx}',
  'lib/**/*.{ts,tsx}',
  'app/**/*.{ts,tsx}',
  '!app/api/**/*.{ts,tsx}',
],
coverageThreshold: {
  global: {
    statements: 80,
    branches: 80,
    functions: 80,
    lines: 80,
  },
},
```

## Dependencies

```json
{
  "devDependencies": {
    "@testing-library/jest-dom": "^6.1.4",
    "@testing-library/react": "^14.0.0",
    "@testing-library/user-event": "^14.5.1",
    "@types/jest": "^29.5.5",
    "jest": "^29.7.0",
    "jest-environment-jsdom": "^29.7.0",
    "msw": "^1.3.2",
    "node-mocks-http": "^1.13.0",
    "@playwright/test": "^1.38.1",
    "supertest": "^6.3.3"
  }
}
```
