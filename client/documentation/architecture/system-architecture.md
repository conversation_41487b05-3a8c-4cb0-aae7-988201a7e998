# System Architecture

## Overview

This document outlines the implementation of an **admin interface system** using **Next.js 15+** as the frontend framework and **Auth.js v5** (NextAuth.js v5) for authentication and authorization. The system includes user and role management, dynamic application control, microfrontend-based modular applications using **Module Federation**, and comprehensive access control mechanisms.

## Core Components

### 1. Admin Interface

The admin interface allows system administrators to manage **users, roles, and permissions**. These management pages are built within a Next.js application and protected using **Auth.js authentication and authorization mechanisms**. Only authorized administrators can access these features.

#### 1.1 User Management (CRUD)

Admins can create, edit, delete, and query users through the admin UI:

- **Frontend Interface**: Using Next.js 15's App Router structure, the user management pages are placed under `app/admin/users`. Components like user lists, forms, and modals are built using React Hook Form.

- **API & Backend Services**: Next.js API Routes or Server Actions handle backend logic. When an admin adds, updates, or deletes a user, the frontend calls the corresponding API (e.g., `POST /api/admin/users`), which processes the request using Prisma ORM.

- **Database Schema**:
  - The `Users` table stores user details, including `id`, `name`, `email`, `passwordHash`, `created_at`, and `updated_at`.
  - Passwords are securely hashed before storage.
  - Indexing is applied to the `email` field for faster lookups.

- **Role-Based Access Control (RBAC)**:
  - Only admins can access the user management API.
  - A middleware in Next.js checks user roles before allowing access.

- **Security Best Practices**:
  - Email uniqueness is enforced to prevent duplicate accounts.
  - Soft delete is used instead of permanently deleting users to maintain audit logs.
  - All actions are logged in the Audit Logs table.

#### 1.2 Role Management

Roles define which pages and features a user can access. Admins can create roles and assign fine-grained permissions:

- **Frontend Interface**: The role management page (`/admin/roles`) lists all roles. Admins can select specific permissions for each role using multi-select lists.

- **Database Schema**:
  - The `Roles` table stores role names and descriptions.
  - The `Permissions` table defines available actions, such as `User:Create`, `Report:View`, etc.
  - The `RolePermissions` junction table maps roles to permissions.

- **Access Control Enforcement**:
  - Permissions are enforced on both frontend and backend:
    - **Frontend**: UI components (buttons, menus) are hidden for unauthorized users.
    - **Backend**: Middleware validates permissions before executing API requests.

#### 1.3 User-Role Mapping

Each user can be assigned multiple roles, allowing flexible access control:

- **Implementation**:
  - Admins can assign or revoke roles from the user management page.
  - The `UserRoles` table stores user-role relationships.

- **Handling Role Changes**:
  - When a user's role changes, their session should be refreshed to reflect updated permissions.
  - JWT tokens might need to be re-issued to apply changes immediately.

#### 1.4 Role-Application Mapping

Roles define which applications a user can access:

- **Database Schema**:
  - The `Applications` table lists available applications.
  - The `RoleApplications` table links roles to applications.

- **Frontend & Backend Control**:
  - The navigation menu dynamically hides applications the user has no access to.
  - API requests are checked against `RoleApplications`.

### 2. Application Management

The admin panel allows enabling/disabling applications dynamically:

#### 2.1 Enabling/Disabling Applications

- Admins can toggle applications ON/OFF from the admin dashboard.
- A boolean flag (`is_active`) in the `Applications` table controls availability.
- When an application is disabled:
  - It disappears from the navigation menu.
  - Any direct access results in a 403 Forbidden response.

#### 2.2 Dynamic Menu Management

- Admins can customize the system menu, including:
  - Adding new items
  - Rearranging order
  - Hiding/unhiding menu items
- The `MenuItems` table stores navigation data.
- The frontend fetches menu configurations dynamically.

## Architecture Diagrams

### Role-Based Access Control (RBAC) Flow

```mermaid
flowchart LR
    User[User] -->|Has| Role[Role]
    Role -->|Grants| Permission[Permission]
    
    subgraph Authorization Process
      Req[User Request] --> MW[Next.js Middleware]
      MW -->|Validate Role| Auth[Auth.js]
      Auth -->|Returns Role List| MW
      MW -->|Check Permissions| Page[Target Page/API]
      MW -->|Deny Access| Denied[Access Denied]
    end
```

### Application Management Architecture

```mermaid
flowchart TD
    Admin[Admin User] -->|Manages| AppConfig[Application Configuration]
    AppConfig -->|Controls| AppState[Application State]
    AppState -->|Determines| MenuItems[Menu Items]
    AppState -->|Affects| AccessControl[Access Control]
    
    subgraph "Runtime Behavior"
      User[End User] --> Menu[Navigation Menu]
      Menu -->|Shows only| AvailableApps[Available Applications]
      User -->|Attempts to access| AppPage[Application Page]
      AppPage --> AccessCheck{Access Check}
      AccessCheck -->|Allowed| RenderApp[Render Application]
      AccessCheck -->|Denied| ErrorPage[403 Error Page]
    end
```

## Implementation Guidelines

### Middleware for Role-Based Protection

```javascript
import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

export async function middleware(req) {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  const { pathname } = req.nextUrl;
  
  if (!token) return NextResponse.redirect(new URL("/login", req.url));
  
  if (pathname.startsWith("/admin") && token.role !== "admin") {
    return NextResponse.redirect(new URL("/no-access", req.url));
  }
  
  return NextResponse.next();
}
```

### Dynamic Permission Checking

```typescript
// Check if user has permission to perform an action
export function hasPermission(
  user: User,
  requiredPermission: string
): boolean {
  if (!user || !user.roles) return false;
  
  // Get all permissions from user's roles
  const userPermissions = user.roles.flatMap(role => role.permissions);
  
  // Check if required permission exists in user permissions
  return userPermissions.some(p => p.name === requiredPermission);
}
```

## Best Practices

1. **Security First**: Implement proper authentication and authorization at all levels.
2. **Performance Optimization**: Use caching for permission checks and menu items.
3. **Error Handling**: Provide clear error messages for unauthorized access.
4. **Audit Logging**: Log all administrative actions for accountability.
5. **Scalability**: Design the system to handle a growing number of users and roles.
6. **Testing**: Thoroughly test access control mechanisms with different user roles.
