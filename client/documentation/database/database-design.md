# Database Design

## Overview

The database design for this application is built using PostgreSQL and Prisma ORM. It provides a robust foundation for user authentication, role-based access control, audit logging, and application management.

## Database Schema

The database schema is defined using Prisma Schema Language and includes the following models:

### Core Models

#### User

The central entity representing application users.

```prisma
model User {
  id                    String                 @id @default(cuid())
  name                  String?
  email                 String                 @unique
  emailVerified         DateTime?
  image                 String?
  password              String?
  status                UserStatus             @default(pending)
  isTwoFactorEnabled    Boolean                @default(false)
  loginAttempts         Int                    @default(0)
  lastLoginAttempt      DateTime?
  lastSuccessfulLogin   DateTime?
  loginAttemptsResetAt  DateTime?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  
  // Relationships
  accounts              Account[]
  auditLogs             AuditLog[]
  loginMethods          LoginMethod[]
  resetTokens           PasswordResetToken[]
  sessions              Session[]
  twoFactorConfirmation TwoFactorConfirmation?
  twoFactorTokens       TwoFactorToken[]
  userRoles             UserRole[]
}
```

#### Account

External authentication provider accounts linked to users.

```prisma
model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @default(now())
  
  // Relationships
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
  @@index([userId])
}
```

#### Session

User sessions for authentication and tracking.

```prisma
model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  lastActivity DateTime @default(now())
  userAgent    String?
  ipAddress    String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now())
  deviceId     String?
  sessionType  String   @default("web")
  
  // Relationships
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([deviceId])
}
```

### Authentication Models

#### TwoFactorConfirmation

Tracks users who have confirmed two-factor authentication.

```prisma
model TwoFactorConfirmation {
  id        String   @id @default(cuid())
  userId    String   @unique
  createdAt DateTime @default(now())
  
  // Relationships
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

#### TwoFactorToken

Tokens used for two-factor authentication.

```prisma
model TwoFactorToken {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expires   DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())
  
  // Relationships
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, token])
  @@index([userId])
}
```

#### VerificationToken

Tokens for email verification.

```prisma
model VerificationToken {
  id      String   @id @default(cuid())
  email   String
  token   String   @unique
  expires DateTime

  @@unique([email, token])
}
```

#### PasswordResetToken

Tokens for password reset.

```prisma
model PasswordResetToken {
  id      String   @id @default(cuid())
  email   String
  token   String   @unique
  expires DateTime
  userId  String?
  
  // Relationships
  user    User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([email])
}
```

#### LoginMethod

Tracks authentication methods used by users.

```prisma
model LoginMethod {
  id        String   @id @default(cuid())
  userId    String
  method    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}
```

### Access Control Models

#### Role

Defines user roles for access control.

```prisma
model Role {
  id          String           @id @default(cuid())
  name        String           @unique
  description String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  
  // Relationships
  users       UserRole[]
  permissions RolePermission[]
  applications RoleApplication[]
}
```

#### Permission

Defines granular permissions that can be assigned to roles.

```prisma
model Permission {
  id          String           @id @default(cuid())
  name        String           @unique
  description String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  
  // Relationships
  roles       RolePermission[]
}
```

#### UserRole

Many-to-many relationship between users and roles.

```prisma
model UserRole {
  id        String   @id @default(cuid())
  userId    String
  roleId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  // Relationships
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  role      Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@index([userId])
  @@index([roleId])
}
```

#### RolePermission

Many-to-many relationship between roles and permissions.

```prisma
model RolePermission {
  id           String     @id @default(cuid())
  roleId       String
  permissionId String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
  
  // Relationships
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@index([roleId])
  @@index([permissionId])
}
```

### Application Management Models

#### Application

Represents applications or modules within the system.

```prisma
model Application {
  id          String             @id @default(cuid())
  name        String             @unique
  displayName String
  description String?
  isActive    Boolean            @default(true)
  path        String             @unique
  icon        String?
  order       Int                @default(0)
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  
  // Relationships
  roles       RoleApplication[]
  menuItems   MenuItem[]
}
```

#### RoleApplication

Many-to-many relationship between roles and applications.

```prisma
model RoleApplication {
  id            String      @id @default(cuid())
  roleId        String
  applicationId String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  
  // Relationships
  role          Role        @relation(fields: [roleId], references: [id], onDelete: Cascade)
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@unique([roleId, applicationId])
  @@index([roleId])
  @@index([applicationId])
}
```

#### MenuItem

Navigation menu items for applications.

```prisma
model MenuItem {
  id            String      @id @default(cuid())
  name          String
  displayName   String
  path          String
  icon          String?
  parentId      String?
  applicationId String
  order         Int         @default(0)
  isVisible     Boolean     @default(true)
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
  
  // Relationships
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  parent        MenuItem?   @relation("MenuItemToMenuItem", fields: [parentId], references: [id], onDelete: SetNull)
  children      MenuItem[]  @relation("MenuItemToMenuItem")

  @@index([applicationId])
  @@index([parentId])
}
```

### Audit and Logging Models

#### AuditLog

Tracks user actions for security and compliance.

```prisma
model AuditLog {
  id           String   @id @default(cuid())
  userId       String?
  action       String
  status       String
  timestamp    DateTime @default(now())
  ipAddress    String?
  userAgent    String?
  targetUserId String?
  resourceId   String?
  resourceType String?
  oldValue     String?
  newValue     String?
  reason       String?
  metadata     Json?
  priority     String   @default("low")
  sessionId    String?
  
  // Relationships
  user         User?    @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([timestamp])
  @@index([targetUserId])
  @@index([priority])
}
```

### Enums

```prisma
enum DefaultRole {
  user
  admin
}

enum UserStatus {
  pending
  active
  suspended
  banned
  deleted
  inactive
}
```

## Database Relationships

The database schema defines several key relationships:

1. **User-Role Relationship**: Many-to-many relationship through the `UserRole` model.
2. **Role-Permission Relationship**: Many-to-many relationship through the `RolePermission` model.
3. **Role-Application Relationship**: Many-to-many relationship through the `RoleApplication` model.
4. **Application-MenuItem Relationship**: One-to-many relationship.
5. **MenuItem Hierarchy**: Self-referential relationship for menu item hierarchy.
6. **User Authentication**: One-to-many relationships with authentication-related models.

## Database Migrations

Database migrations are managed through Prisma Migrate. To create and apply migrations:

```bash
# Generate a migration
npx prisma migrate dev --name migration_name

# Apply migrations in production
npx prisma migrate deploy
```

## Database Seeding

Initial data seeding is handled through the Prisma seed script:

```bash
# Run database seeding
npx prisma db seed
```

## Best Practices

1. **Use Transactions**: For operations that modify multiple records.
2. **Indexing**: Important fields are indexed for performance.
3. **Cascading Deletes**: Configured for parent-child relationships.
4. **Audit Logging**: Use the AuditLog model to track important changes.
5. **Soft Deletes**: User status can be set to 'deleted' instead of removing records.

## Security Considerations

1. **Password Storage**: Passwords are hashed and never stored in plain text.
2. **Session Management**: Sessions include IP and user agent tracking.
3. **Rate Limiting**: Login attempts are tracked to prevent brute force attacks.
4. **Audit Logging**: Security-relevant actions are logged with appropriate priority.
