# Authentication Specification

## 1. Overview & Objectives

The authentication system provides secure user identity verification and session management using Auth.js (NextAuth.js) Beta with multiple authentication providers.

### Primary Goals
- Implement secure authentication flows
- Support multiple auth providers
- Manage user sessions
- Handle password reset/recovery
- Provide 2FA capabilities

## 2. Scope & Constraints

### In Scope
- Email/password authentication
- OAuth provider integration
- Session management
- Password recovery
- Two-factor authentication
- JWT handling

### Constraints
- Auth.js Beta compatibility
- Edge runtime support
- No client-side JWT storage
- Secure password hashing
- Rate limiting requirements

## 3. Functional Requirements

### Authentication Methods
```typescript
const authConfig = {
  providers: [
    EmailProvider(),
    GoogleProvider(),
    GitHubProvider(),
    CredentialsProvider(),
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
};
```

### Session Management
```typescript
interface Session {
  user: {
    id: string;
    email: string;
    name?: string;
    image?: string;
    role: UserRole;
  };
  expires: ISODateString;
}
```

## 4. Technical Requirements & Architecture

### Technology Stack
- Auth.js (NextAuth.js) Beta
- Prisma adapter
- JWT tokens
- Edge compatibility
- Redis session store

### Authentication Flow
1. User initiates auth
2. Provider verification
3. Session creation
4. JWT generation
5. Cookie setting

## 5. Performance & Scalability Expectations

### Response Times
- Login process: < 2s
- Session validation: < 100ms
- Token refresh: < 500ms
- Provider redirect: < 1s

### Scalability
- Horizontal scaling support
- Redis session clustering
- Token rotation strategy
- Rate limiting per IP

## 6. Error Handling & Security

### Security Measures
- Password hashing (Argon2)
- CSRF protection
- Rate limiting
- Session invalidation
- IP blocking

### Error Responses
```typescript
type AuthError = {
  code: string;
  message: string;
  status: number;
};

const authErrors = {
  INVALID_CREDENTIALS: {
    code: 'auth/invalid-credentials',
    message: 'Invalid email or password',
    status: 401,
  },
};
```

## 7. Authentication Flows

### 7.1 Email/Password Login Flow

1. User enters email and password
2. System validates credentials
3. If valid, system checks for 2FA requirement
4. If 2FA is required, user is prompted for code
5. Upon successful authentication, JWT is generated
6. User is redirected to dashboard

### 7.2 OAuth Provider Flow

1. User clicks OAuth provider button
2. User is redirected to provider's login page
3. User authenticates with provider
4. Provider redirects back with authorization code
5. System exchanges code for access token
6. System creates or updates user record
7. JWT is generated and user is logged in

### 7.3 Password Reset Flow

1. User requests password reset
2. System generates unique token and sends email
3. User clicks link in email
4. System validates token
5. User enters new password
6. System updates password and invalidates token

### 7.4 Two-Factor Authentication Flow

1. User enables 2FA in settings
2. System generates TOTP secret
3. User scans QR code with authenticator app
4. User verifies setup with code from app
5. System enables 2FA for user account
6. Future logins require TOTP code

## 8. Implementation Details

### 8.1 Auth.js Configuration

```typescript
// auth.ts
import NextAuth from "next-auth";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { db } from "@/lib/db";
import authConfig from "@/auth.config";
import { getUserById } from "@/data/user";
import { getTwoFactorConfirmationByUserId } from "@/data/two-factor-confirmation";
import { getAccountByUserId } from "@/data/account";

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  events: {
    async linkAccount({ user }) {
      await db.user.update({
        where: { id: user.id },
        data: { emailVerified: new Date() }
      });
    }
  },
  callbacks: {
    async signIn({ user, account }) {
      // Allow OAuth without email verification
      if (account?.provider !== "credentials") return true;

      const existingUser = await getUserById(user.id);

      // Prevent sign in without email verification
      if (!existingUser?.emailVerified) return false;

      // 2FA Check
      if (existingUser.isTwoFactorEnabled) {
        const twoFactorConfirmation = await getTwoFactorConfirmationByUserId(existingUser.id);

        if (!twoFactorConfirmation) return false;

        // Delete two factor confirmation for next sign in
        await db.twoFactorConfirmation.delete({
          where: { id: twoFactorConfirmation.id }
        });
      }

      return true;
    },
    async session({ token, session }) {
      if (token.sub && session.user) {
        session.user.id = token.sub;
      }

      if (token.role && session.user) {
        session.user.role = token.role as UserRole;
      }

      return session;
    },
    async jwt({ token }) {
      if (!token.sub) return token;

      const existingUser = await getUserById(token.sub);

      if (!existingUser) return token;

      const existingAccount = await getAccountByUserId(
        existingUser.id
      );

      token.role = existingUser.role;
      token.isTwoFactorEnabled = existingUser.isTwoFactorEnabled;
      token.isOAuth = !!existingAccount;

      return token;
    }
  },
  adapter: PrismaAdapter(db),
  session: { strategy: "jwt" },
  ...authConfig,
});
```

### 8.2 Middleware for Protected Routes

```typescript
// middleware.ts
import NextAuth from "next-auth";
import authConfig from "@/auth.config";
import {
  DEFAULT_LOGIN_REDIRECT,
  apiAuthPrefix,
  authRoutes,
  publicRoutes,
} from "@/routes";

const { auth } = NextAuth(authConfig);

export default auth((req) => {
  const { nextUrl } = req;
  const isLoggedIn = !!req.auth;

  const isApiAuthRoute = nextUrl.pathname.startsWith(apiAuthPrefix);
  const isPublicRoute = publicRoutes.includes(nextUrl.pathname);
  const isAuthRoute = authRoutes.includes(nextUrl.pathname);

  if (isApiAuthRoute) {
    return null;
  }

  if (isAuthRoute) {
    if (isLoggedIn) {
      return Response.redirect(new URL(DEFAULT_LOGIN_REDIRECT, nextUrl))
    }
    return null;
  }

  if (!isLoggedIn && !isPublicRoute) {
    let callbackUrl = nextUrl.pathname;
    if (nextUrl.search) {
      callbackUrl += nextUrl.search;
    }

    const encodedCallbackUrl = encodeURIComponent(callbackUrl);

    return Response.redirect(new URL(
      `/auth/login?callbackUrl=${encodedCallbackUrl}`,
      nextUrl
    ));
  }

  return null;
});

export const config = {
  matcher: ['/((?!.+\\.[\\w]+$|_next).*)', '/', '/(api|trpc)(.*)'],
};
```

## 9. Testing & Acceptance Criteria

### Test Cases
- Login flows
- Registration process
- Password reset
- Session management
- Provider integration
- Error handling

### Acceptance Criteria
- Secure password storage
- Multiple provider support
- Session persistence
- Rate limiting
- Error handling
- Edge compatibility

## 10. Role-Based Access Control

### User Roles

1. **User**: Regular user with basic access
2. **Admin**: Administrator with elevated privileges
3. **Super-Admin**: Highest level of access with system-wide privileges
4. **Custom Roles**: Additional roles can be defined as needed

### Permissions

Permissions are granular access controls that can be assigned to roles:

1. **Resource-Based**: Permissions for specific resources (users, content, etc.)
2. **Action-Based**: Permissions for specific actions (create, read, update, delete)
3. **Feature-Based**: Permissions for specific features or modules

### Application Access

The system supports controlling access to different applications or modules:

1. **Application Definition**: Applications are defined with paths and metadata
2. **Role-Application Mapping**: Roles can be granted access to specific applications
3. **Menu Item Control**: Menu items are displayed based on user access

## 11. Two-Factor Authentication

The system supports two-factor authentication for enhanced security:

1. **Enabling 2FA**: Users can enable 2FA from their profile settings
2. **Verification Process**: When 2FA is enabled, users must provide a verification code during login
3. **Backup Codes**: Users can generate backup codes for emergency access
4. **Recovery Options**: Process for recovering access if 2FA device is lost

## 12. Security Features

### Password Security

1. **Hashing**: Passwords are hashed using bcrypt
2. **Password Policies**: Enforced minimum length and complexity requirements
3. **Password Reset**: Secure password reset flow with expiring tokens

### Session Management

1. **JWT Tokens**: Sessions are managed using JWT tokens
2. **Session Expiry**: Sessions expire after a configurable period
3. **Session Tracking**: Sessions include IP address and user agent information
4. **Session Invalidation**: Ability to invalidate all sessions for a user

### Brute Force Protection

1. **Login Attempt Tracking**: System tracks failed login attempts
2. **Account Lockout**: Temporary account lockout after multiple failed attempts
3. **Progressive Delays**: Increasing delays between login attempts

### Audit Logging

1. **Authentication Events**: Logging of login, logout, and authentication failures
2. **Access Control Events**: Logging of access attempts and authorization decisions
3. **Security Events**: Logging of security-relevant actions (password changes, etc.)

## 13. API Endpoints

### Authentication Endpoints

1. `/api/auth/login`: Email/password login
2. `/api/auth/register`: User registration
3. `/api/auth/verify-email`: Email verification
4. `/api/auth/reset-password`: Password reset
5. `/api/auth/two-factor`: Two-factor authentication

### User Management Endpoints

1. `/api/users/me`: Get current user information
2. `/api/users/:id`: Get, update, or delete user
3. `/api/users/:id/roles`: Manage user roles

### Role Management Endpoints

1. `/api/roles`: List, create, update, or delete roles
2. `/api/roles/:id/permissions`: Manage role permissions
3. `/api/roles/:id/applications`: Manage role application access

## 14. Client-Side Integration

### Authentication Hooks

1. `useAuth()`: Hook for accessing authentication state and methods
2. `useUser()`: Hook for accessing current user information
3. `useRoles()`: Hook for checking user roles and permissions

### Protected Components

1. `<AuthGuard>`: Component for protecting routes based on authentication
2. `<RoleGuard>`: Component for protecting routes based on roles
3. `<PermissionGuard>`: Component for protecting UI elements based on permissions

## 15. Testing

1. **Unit Tests**: Testing individual authentication components
2. **Integration Tests**: Testing authentication flows
3. **E2E Tests**: Testing complete authentication journeys

## 16. Best Practices

1. **Use HTTPS**: Always use HTTPS in production
2. **Secure Cookies**: Use secure and HTTP-only cookies
3. **Input Validation**: Validate all user inputs
4. **Error Handling**: Provide generic error messages to users
5. **Rate Limiting**: Implement rate limiting for authentication endpoints
6. **Security Headers**: Use appropriate security headers
7. **Regular Auditing**: Regularly audit authentication logs
