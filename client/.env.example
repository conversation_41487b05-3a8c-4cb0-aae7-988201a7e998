# Auth.js required
AUTH_SECRET="your_auth_secret_here" # Added by `npx auth`. Read more: https://cli.authjs.dev
AUTH_URL=http://localhost:3000

# OAuth Providers
GITHUB_CLIENT_ID=your_github_client_id_here
GITHUB_CLIENT_SECRET=your_github_client_secret_here

GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Database
DATABASE_URL="your_database_url_here"

# Email
RESEND_API_KEY=your_resend_api_key_here

# Development
NODE_ENV="development"
DEFAULT_LOGIN_REDIRECT=/dashboard

# Logging
ELASTICSEARCH_URL=http://localhost:9200
APP_VERSION=1.0.0
LOG_LEVEL=debug # production should use 'info'
NEXT_PUBLIC_CLIENT_SIDE_LOG_ON=true
SERVER_SIDE_LOG_ON=true
NEXT_PUBLIC_LOG_LEVEL=info
#NEXT_PUBLIC_LOG_LEVEL=info|warn|error|debug to control log level

# Micro-frontend Configuration
# Remote Module URLs for Module Federation
NEXT_PUBLIC_ADMIN_APP_URL=http://localhost:3001
NEXT_PUBLIC_REPORTS_APP_URL=http://localhost:3002
NEXT_PUBLIC_ANALYTICS_APP_URL=http://localhost:3003
NEXT_PUBLIC_HOST_APP_URL=http://localhost:3000
