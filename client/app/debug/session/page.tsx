'use client';

import { useSession, signIn, signOut, getCsrfToken } from 'next-auth/react';
import { useEffect, useState } from 'react';

export default function DebugSessionPage() {
  const { data: session, status, update } = useSession();
  const [cookies, setCookies] = useState<string>('');
  const [csrfToken, setCsrfToken] = useState<string | undefined>();

  useEffect(() => {
    setCookies(document.cookie);
    
    // Get CSRF token for auth
    getCsrfToken().then(token => {
      console.log("CSRF Token:", token);
      setCsrfToken(token || undefined);
    });
  }, []);

  // Create a more detailed view of the cookies with proper handling of empty strings
  const parsedCookies = cookies ? cookies.split('; ').map(cookie => {
    const [name, value] = cookie.split('=');
    return { name, value: value || '' };
  }) : [];

  const refreshSession = async () => {
    await update();
    setCookies(document.cookie);
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Session Debug</h1>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Session Status</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-40">
          {status}
        </pre>
      </div>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Session Data</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-80">
          {JSON.stringify(session, null, 2)}
        </pre>
      </div>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Cookies</h2>
        <div className="bg-gray-100 p-4 rounded overflow-auto max-h-80">
          {parsedCookies.length === 0 ? (
            <p>No cookies found</p>
          ) : (
            parsedCookies.map((cookie, index) => (
              <div key={index} className="mb-2">
                <strong>{cookie.name || 'unknown'}</strong>: {cookie.value && cookie.value.length > 50 
                  ? `${cookie.value.substring(0, 50)}...` 
                  : (cookie.value || 'empty')}
              </div>
            ))
          )}
        </div>
      </div>
      
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2">Auth Session Cookie Info</h2>
        <div className="bg-gray-100 p-4 rounded overflow-auto max-h-80">
          <p>Looking for these cookies:</p>
          <ul className="list-disc ml-5 mt-2">
            <li><strong>next-auth.session-token</strong>: {parsedCookies.some(c => c.name === 'next-auth.session-token') ? 'Found ✅' : 'Not found ❌'}</li>
            <li><strong>__Secure-next-auth.session-token</strong>: {parsedCookies.some(c => c.name === '__Secure-next-auth.session-token') ? 'Found ✅' : 'Not found ❌'}</li>
            <li><strong>next-auth.csrf-token</strong>: {parsedCookies.some(c => c.name === 'next-auth.csrf-token') ? 'Found ✅' : 'Not found ❌'}</li>
            <li><strong>authjs.csrf-token</strong>: {parsedCookies.some(c => c.name === 'authjs.csrf-token') ? 'Found ✅' : 'Not found ❌'}</li>
          </ul>
          
          <div className="mt-4">
            <p><strong>CSRF Token:</strong> {csrfToken || 'Not available'}</p>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h2 className="text-xl font-semibold mb-2">Authentication Actions</h2>
          <div className="flex gap-2 flex-wrap">
            <button 
              onClick={() => signIn()}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            >
              Sign In
            </button>
            <button 
              onClick={() => signOut()}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
            >
              Sign Out
            </button>
            <button 
              onClick={refreshSession}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
            >
              Refresh Session
            </button>
          </div>
        </div>
        
        <div>
          <h2 className="text-xl font-semibold mb-2">Test Login</h2>
          <div className="flex gap-2 flex-wrap">
            <button 
              onClick={() => signIn('credentials', { 
                email: '<EMAIL>', 
                password: 'password123',
                callbackUrl: '/debug/session',
                redirect: true
              })}
              className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded"
            >
              Login as Test User
            </button>
            <button 
              onClick={async () => {
                try {
                  const response = await fetch(
                    "/api/auth/sign-in-test?email=<EMAIL>&password=password123&returnTo=/debug/session"
                  );
                  const data = await response.json();
                  console.log("Server auth response:", data);
                  
                  if (data.success && data.redirectTo) {
                    // Update session before redirecting
                    await update();
                    window.location.href = data.redirectTo;
                  } else {
                    console.error("Auth failed:", data.error || "Unknown error");
                  }
                } catch (error) {
                  console.error("Auth request error:", error);
                }
              }}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
            >
              Server Auth Action
            </button>
            <button
              onClick={() => window.location.href = '/api/auth/signin/credentials?callbackUrl=/debug/session&email=<EMAIL>&password=password123'}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
            >
              Direct Auth.js URL
            </button>
            <button
              onClick={() => {
                const form = document.createElement('form');
                form.method = 'post';
                form.action = '/api/auth/callback/credentials';
                
                // Add CSRF token if available
                if (csrfToken) {
                  const csrfInput = document.createElement('input');
                  csrfInput.type = 'hidden';
                  csrfInput.name = 'csrfToken';
                  csrfInput.value = csrfToken;
                  form.appendChild(csrfInput);
                }
                
                // Add credentials
                const emailInput = document.createElement('input');
                emailInput.type = 'hidden';
                emailInput.name = 'email';
                emailInput.value = '<EMAIL>';
                form.appendChild(emailInput);
                
                const passwordInput = document.createElement('input');
                passwordInput.type = 'hidden';
                passwordInput.name = 'password';
                passwordInput.value = 'password123';
                form.appendChild(passwordInput);
                
                // Add redirect URL
                const redirectInput = document.createElement('input');
                redirectInput.type = 'hidden';
                redirectInput.name = 'callbackUrl';
                redirectInput.value = '/debug/session';
                form.appendChild(redirectInput);
                
                // Submit the form
                document.body.appendChild(form);
                form.submit();
              }}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
            >
              Form Submit Login
            </button>
            <a 
              href="/api/auth/test-user"
              className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded inline-block"
            >
              Create Test User
            </a>
            <a 
              href="/auth/login?callbackUrl=/debug/session&email=<EMAIL>"
              className="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded inline-block"
            >
              Login Form
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}