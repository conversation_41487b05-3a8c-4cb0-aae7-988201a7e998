'use client';

import { useState } from 'react';
import { signIn } from 'next-auth/react';

export default function TestAuthPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password123');
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Test our custom login API
  const testLogin = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      const response = await fetch('/api/auth/test-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });
      
      const data = await response.json();
      if (response.ok) {
        setResult(data);
        
        // If we have a redirectUrl from our API, we can use it
        if (data.redirectUrl) {
          window.location.href = data.redirectUrl;
          return;
        }
      } else {
        setError(data.error || 'Unknown error');
      }
    } catch (err) {
      setError(`Request failed: ${(err as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  // Use Auth.js client-side signIn function
  const testClientSignIn = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    
    try {
      // Use the Auth.js signIn function directly
      const response = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });
      
      setResult(response);
      
      if (response?.error) {
        setError(response.error);
      } else if (response?.ok) {
        // Refresh the page to reflect the new session
        window.location.href = '/debug/session';
      }
    } catch (err) {
      setError(`Auth.js signIn failed: ${(err as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Authentication Test Page</h1>
      
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">Email</label>
        <input
          type="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className="w-full border rounded p-2 mb-4"
        />
        
        <label className="block text-sm font-medium mb-2">Password</label>
        <input
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className="w-full border rounded p-2 mb-4"
        />
        
        <div className="flex gap-4">
          <button
            onClick={testLogin}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
          >
            {loading ? 'Testing...' : 'Test Login Logic'}
          </button>
          
          <button
            onClick={testClientSignIn}
            disabled={loading}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded"
          >
            {loading ? 'Testing...' : 'Client signIn()'}
          </button>
          
          <a 
            href={`/api/auth/signin?callbackUrl=/dashboard&email=${encodeURIComponent(email)}`}
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded inline-block"
          >
            Use Standard Flow
          </a>
        </div>
      </div>
      
      {error && (
        <div className="mb-6 p-4 bg-red-100 border border-red-400 rounded">
          <h2 className="text-xl font-semibold mb-2 text-red-800">Error</h2>
          <p className="text-red-700">{error}</p>
        </div>
      )}
      
      {result && (
        <div className="mb-6">
          <h2 className="text-xl font-semibold mb-2">Result</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto max-h-80">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
      
      <div className="mt-6">
        <h2 className="text-xl font-semibold mb-2">Navigation</h2>
        <div className="flex gap-4">
          <a 
            href="/debug/session"
            className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded inline-block"
          >
            Check Session
          </a>
          
          <a 
            href="/api/auth/test-user"
            className="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded inline-block"
          >
            Create Test User
          </a>
          
          <a 
            href={`/auth/login?callbackUrl=/dashboard&email=${encodeURIComponent(email)}`}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded inline-block"
          >
            Use Standard Login Form
          </a>
          
          <button
            onClick={() => {
              // Direct call to the client library, bypassing our test page
              signIn('credentials', {
                callbackUrl: '/dashboard',
                email,
                password,
                redirect: true
              });
            }}
            className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded"
          >
            Direct Auth.js Login
          </button>
        </div>
      </div>
    </div>
  );
}