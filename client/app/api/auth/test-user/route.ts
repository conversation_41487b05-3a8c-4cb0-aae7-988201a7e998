/**
 * @fileoverview Debug route to create a test user with known password
 * WARNING: ONLY FOR DEVELOPMENT - REMOVE IN PRODUCTION
 */

import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { hashPassword } from "@/lib/crypto";

export async function GET() {
  // Only allow in development
  if (process.env.NODE_ENV === "production") {
    return NextResponse.json({ error: "Not available in production" }, { status: 403 });
  }

  try {
    // Check if test user already exists
    const email = "<EMAIL>";
    let user = await db.user.findUnique({ where: { email } });

    if (user) {
      // Update the existing user with a new password
      user = await db.user.update({
        where: { email },
        data: {
          password: await hashPassword("password123"),
          status: "active",
          role: "user",
          emailVerified: new Date(),
        }
      });
      
      // Generate login URL with CSRF protection using standard login form
      const loginUrl = "/auth/login?callbackUrl=/dashboard&email=" + encodeURIComponent(email);
      
      return NextResponse.json({
        message: "Test user updated. Login using the standard login form.",
        email,
        password: "password123",
        userId: user.id,
        loginUrl,
        instruction: "For reliable login that handles CSRF tokens, use the standard login form with these credentials."
      });
    } else {
      // Create a new test user
      user = await db.user.create({
        data: {
          name: "Test User",
          email,
          password: await hashPassword("password123"),
          status: "active",
          role: "user",
          emailVerified: new Date(),
        }
      });
      
      // Generate login URL with CSRF protection using standard login form
      const loginUrl = "/auth/login?callbackUrl=/dashboard&email=" + encodeURIComponent(email);
      
      return NextResponse.json({
        message: "Test user created. Login using the standard login form.",
        email,
        password: "password123",
        userId: user.id,
        loginUrl,
        instruction: "For reliable login that handles CSRF tokens, use the standard login form with these credentials."
      });
    }
  } catch (error) {
    console.error("Error creating test user:", error);
    return NextResponse.json({ error: "Failed to create test user" }, { status: 500 });
  }
}