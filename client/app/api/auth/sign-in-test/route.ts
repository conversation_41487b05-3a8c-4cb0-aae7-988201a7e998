/**
 * Special test endpoint to diagnose session issues
 */
import { NextRequest, NextResponse } from "next/server";
import { cookies } from "next/headers";
import { signIn } from "@/auth";
import { db } from "@/lib/db";

export async function GET(req: NextRequest) {
  if (process.env.NODE_ENV === "production") {
    return NextResponse.json({ error: "Not available in production" }, { status: 403 });
  }

  try {
    // Get query parameters
    const url = new URL(req.url);
    const email = url.searchParams.get("email") || "<EMAIL>";
    const password = url.searchParams.get("password") || "password123";
    const returnTo = url.searchParams.get("returnTo") || "/debug/session";

    try {
      // Manual sign-in with next-auth server action (disable built-in redirect)
      const result = await signIn("credentials", { 
        email, 
        password,
        redirect: false
      });
      
      console.log("Auth result:", result);
      
      // Get all cookies to display in response
      const cookieStore = await cookies();
      const allCookies = cookieStore.getAll();
      const cookieInfo = allCookies.map(c => ({
        name: c.name,
        value: c.value.substring(0, 15) + "..." // Truncate for security
      }));

      // Return a JSON response with debug info
      return NextResponse.json({
        success: true,
        message: "Sign-in attempt processed. Check cookies for session token.",
        cookies: cookieInfo,
        redirectTo: returnTo
      });
    } catch (authError: any) {
      console.log("Authentication error:", authError);
      
      // Return a meaningful error message for authentication failures
      return NextResponse.json({ 
        success: false,
        error: "Invalid email or password",
        errorType: authError.type,
        errorDetails: authError
      }, { status: 401 });
    }
  } catch (error) {
    console.error("Sign-in test error:", error);
    return NextResponse.json({ 
      success: false,
      error: "Error processing sign-in test: " + (error as Error).message 
    }, { status: 500 });
  }
}