/**
 * @fileoverview Auth.js route handler for Next.js App Router
 * @module app/api/auth/[...nextauth]
 * @description Handles all authentication requests including:
 * - Sign in/out
 * - Session management
 * - OAuth provider callbacks
 * - JWT operations
 * - CSRF protection
 */

import { handlers } from "@/auth";
import { NextRequest } from "next/server";

/**
 * Auth.js route handlers for HTTP methods with added logging
 * 
 * @description
 * - GET: Handles session validation and provider callbacks
 * - POST: Handles authentication requests and token operations
 */

// Add logging middleware
function withLogging(handler: any) {
  return async (req: NextRequest, ...args: any[]) => {
    console.log(`Auth API request: ${req.method} ${req.nextUrl.pathname}${req.nextUrl.search}`);
    console.log("Auth request cookies:", req.cookies.toString());
    
    // Log headers
    console.log("Auth request headers:", Object.fromEntries(req.headers));
    
    // Try to log body for POST requests
    if (req.method === "POST") {
      try {
        const clonedReq = req.clone();
        const body = await clonedReq.json();
        console.log("Auth request body:", JSON.stringify(body));
      } catch (e) {
        console.log("Could not parse request body");
      }
    }
    
    try {
      const res = await handler(req, ...args);
      console.log(`Auth API response status: ${res.status}`);
      console.log("Auth response cookies:", res.headers.get("set-cookie") || "none");
      return res;
    } catch (error) {
      console.error("Auth API error:", error);
      throw error;
    }
  };
}

// Wrap handlers with logging
export const GET = withLogging(handlers.GET);
export const POST = withLogging(handlers.POST);
