/**
 * Test login endpoint to diagnose authentication issues
 */
import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { verifyPassword } from "@/lib/crypto";

export async function POST(req: NextRequest) {
  if (process.env.NODE_ENV === "production") {
    return NextResponse.json({ error: "Not available in production" }, { status: 403 });
  }

  try {
    const body = await req.json();
    const { email, password } = body;
    
    console.log("Test login attempt for:", email);
    
    // Step 1: Check if user exists in database
    const user = await db.user.findUnique({
      where: { email }
    });
    
    console.log("User found:", user ? "Yes" : "No");
    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }
    
    // Step 2: Check password
    console.log("Checking password...");
    console.log("User has password:", user.password ? "Yes" : "No");
    
    if (!user.password) {
      return NextResponse.json({ error: "User has no password set" }, { status: 400 });
    }
    
    // Log password format
    console.log("Password hash format:", user.password.substring(0, 10) + "...");
    
    let isValid;
    try {
      isValid = await verifyPassword(password, user.password);
      console.log("Password verification result:", isValid);
    } catch (error) {
      console.error("Password verification error:", error);
      return NextResponse.json({ 
        error: "Password verification failed: " + (error as Error).message 
      }, { status: 500 });
    }
    
    if (!isValid) {
      return NextResponse.json({ error: "Invalid password" }, { status: 401 });
    }
    
    // Return success with user info and a URL for client-side redirect
    const signInUrl = `/api/auth/callback/credentials`;
    
    // Include debug info about the auth mechanism
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        status: user.status
      },
      message: "Credentials verified. Use client-side signIn() to complete authentication.",
      debug: {
        authUrl: "/api/auth/signin/credentials",
        nextAuthUrl: process.env.NEXTAUTH_URL || "Not set", 
        nextAuthSecret: process.env.NEXTAUTH_SECRET ? "Set" : "Not set",
        env: process.env.NODE_ENV
      }
    });
    
  } catch (error) {
    console.error("Test login error:", error);
    return NextResponse.json({ 
      error: "Error processing login request: " + (error as Error).message 
    }, { status: 500 });
  }
}