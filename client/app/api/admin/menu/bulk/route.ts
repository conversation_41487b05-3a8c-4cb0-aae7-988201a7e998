import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";

// POST /api/admin/menu/bulk - Bulk update menu items (reordering, etc)
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    // Check if user is authenticated and has admin role
    if (!session?.user || !session.user.role?.includes("admin")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse request body
    const body = await req.json();
    const { items } = body;
    
    // Validate items array
    if (!items || !Array.isArray(items) || items.length === 0) {
      return NextResponse.json(
        { error: "Items array is required" },
        { status: 400 }
      );
    }
    
    // Perform bulk update
    const updatePromises = items.map(item => {
      const { id, order, parentId } = item;
      
      if (!id) return null;
      
      return db.menuItem.update({
        where: { id },
        data: {
          order: order !== undefined ? order : undefined,
          parentId: parentId !== undefined ? parentId : undefined
        }
      });
    }).filter(Boolean); // Filter out null promises
    
    await Promise.all(updatePromises);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[MENU_BULK]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}