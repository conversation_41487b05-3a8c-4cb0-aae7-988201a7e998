import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";

// PUT /api/admin/menu/:id - Update a menu item
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    const { id } = params;
    
    // Check if user is authenticated and has admin role
    if (!session?.user || !session.user.role?.includes("admin")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse request body
    const body = await req.json();
    const { name, displayName, path, icon, parentId, applicationId, order, isVisible } = body;
    
    // Update menu item
    const menuItem = await db.menuItem.update({
      where: { id },
      data: {
        name: name !== undefined ? name : undefined,
        displayName: displayName !== undefined ? displayName : undefined,
        path: path !== undefined ? path : undefined,
        icon: icon !== undefined ? icon : undefined,
        parentId: parentId !== undefined ? parentId : undefined,
        applicationId: applicationId !== undefined ? applicationId : undefined,
        order: order !== undefined ? order : undefined,
        isVisible: isVisible !== undefined ? isVisible : undefined
      }
    });
    
    return NextResponse.json(menuItem);
  } catch (error) {
    console.error("[MENU_PUT]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/menu/:id - Delete a menu item
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    const { id } = params;
    
    // Check if user is authenticated and has admin role
    if (!session?.user || !session.user.role?.includes("admin")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Delete menu item
    await db.menuItem.delete({
      where: { id }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[MENU_DELETE]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}