import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";

// GET /api/admin/menu - Get all menu items
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    // Check if user is authenticated and has admin role
    if (!session?.user || !session.user.role?.includes("admin")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get query params
    const { searchParams } = new URL(req.url);
    const applicationId = searchParams.get("applicationId");
    const section = searchParams.get("section");
    
    // Build base query
    let query: any = {};
    
    // If applicationId is provided, filter by it
    if (applicationId) {
      query.where = { ...query.where, applicationId };
    }
    
    // If section is provided, filter by path starting with that section
    if (section) {
      query.where = { 
        ...query.where, 
        path: {
          startsWith: section
        } 
      };
    }
    
    // Get menu items from database
    const menuItems = await db.menuItem.findMany({
      ...query,
      where: query.where || undefined,
      orderBy: { order: 'asc' },
      include: {
        application: true
      }
    });
    
    // Return the raw menu items directly - the client will handle tree building
    return NextResponse.json(menuItems);
  } catch (error) {
    console.error("[MENU_GET]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/admin/menu - Create a new menu item
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    // Check if user is authenticated and has admin role
    if (!session?.user || !session.user.role?.includes("admin")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Parse request body
    const body = await req.json();
    const { name, displayName, path, icon, parentId, applicationId, order, isVisible } = body;
    
    // Validate required fields
    if (!name || !displayName || !path || !applicationId) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // Create menu item
    const menuItem = await db.menuItem.create({
      data: {
        name,
        displayName,
        path,
        icon,
        parentId,
        applicationId,
        order: order || 0,
        isVisible: isVisible !== undefined ? isVisible : true
      }
    });
    
    return NextResponse.json(menuItem);
  } catch (error) {
    console.error("[MENU_POST]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}