import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/auth";

// GET /api/admin/applications - Get all applications
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    // Check if user is authenticated and has admin role
    if (!session?.user || !session.user.role?.includes("admin")) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }
    
    // Get applications from database
    const applications = await db.application.findMany({
      orderBy: { order: 'asc' }
    });
    
    return NextResponse.json(applications);
  } catch (error) {
    console.error("[APPLICATIONS_GET]", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}