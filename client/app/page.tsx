// app/page.tsx
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";

/**
 * Home component - The main landing page of the application
 * 
 * A news website layout featuring:
 * - Header with navigation
 * - Featured news section
 * - Sidebar with additional news
 * - Responsive grid layout
 * 
 * @component
 * @returns {JSX.Element} The rendered home page
 */
export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-2xl font-bold">
                News Portal
              </Link>
              <div className="hidden md:flex space-x-4">
                <Link href="/politics" className="hover:text-primary">Politics</Link>
                <Link href="/technology" className="hover:text-primary">Technology</Link>
                <Link href="/business" className="hover:text-primary">Business</Link>
                <Link href="/entertainment" className="hover:text-primary">Entertainment</Link>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline">Sign In</Button>
              <Button>Subscribe</Button>
            </div>
          </nav>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Featured News Section */}
          <div className="lg:col-span-8">
            <Card className="mb-8">
              <CardContent className="p-0">
                <div className="relative h-[400px]">
                  <Image
                    src="/placeholder-news.jpg"
                    alt="Featured news"
                    fill
                    className="object-cover rounded-t-lg"
                  />
                </div>
                <div className="p-6">
                  <h2 className="text-2xl font-bold mb-4">Breaking News Headline</h2>
                  <p className="text-muted-foreground mb-4">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  </p>
                  <Button>Read More</Button>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i}>
                  <CardContent className="p-0">
                    <div className="relative h-[200px]">
                      <Image
                        src="/placeholder-news.jpg"
                        alt={`News ${i}`}
                        fill
                        className="object-cover rounded-t-lg"
                      />
                    </div>
                    <div className="p-4">
                      <h3 className="font-semibold mb-2">News Title {i}</h3>
                      <p className="text-sm text-muted-foreground">
                        Brief description of the news article...
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Sidebar */}
          <aside className="lg:col-span-4">
            <Card className="mb-6">
              <CardHeader>
                <CardTitle>Latest News</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="flex items-center space-x-4">
                      <div className="relative w-20 h-20">
                        <Image
                          src="/placeholder-news.jpg"
                          alt={`Thumbnail ${i}`}
                          fill
                          className="object-cover rounded"
                        />
                      </div>
                      <div>
                        <h4 className="font-medium">Latest News Title {i}</h4>
                        <p className="text-sm text-muted-foreground">2 hours ago</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Popular Topics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {['Politics', 'Technology', 'Sports', 'Health', 'Science', 'Entertainment'].map((topic) => (
                    <Button key={topic} variant="outline" size="sm">
                      {topic}
                    </Button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </aside>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t mt-16">
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="font-bold mb-4">About Us</h3>
              <p className="text-sm text-muted-foreground">
                Your trusted source for the latest news and updates.
              </p>
            </div>
            <div>
              <h3 className="font-bold mb-4">Categories</h3>
              <ul className="space-y-2">
                <li><Link href="/politics" className="text-sm hover:text-primary">Politics</Link></li>
                <li><Link href="/technology" className="text-sm hover:text-primary">Technology</Link></li>
                <li><Link href="/business" className="text-sm hover:text-primary">Business</Link></li>
                <li><Link href="/entertainment" className="text-sm hover:text-primary">Entertainment</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="font-bold mb-4">Follow Us</h3>
              <div className="flex space-x-4">
                <Link href="#" className="text-sm hover:text-primary">Twitter</Link>
                <Link href="#" className="text-sm hover:text-primary">Facebook</Link>
                <Link href="#" className="text-sm hover:text-primary">Instagram</Link>
              </div>
            </div>
            <div>
              <h3 className="font-bold mb-4">Subscribe</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Get the latest news delivered to your inbox.
              </p>
              <Button>Subscribe Now</Button>
            </div>
          </div>
          <div className="mt-8 pt-8 border-t text-center text-sm text-muted-foreground">
            2025 News Portal. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
