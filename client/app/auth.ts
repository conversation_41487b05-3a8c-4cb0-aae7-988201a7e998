/**
 * @fileoverview Auth.js configuration and utilities
 * @module app/auth
 * @description Exports configured authentication utilities including:
 * - Authentication handler
 * - Sign in function
 * - Sign out function
 * 
 * This module re-exports the configured Auth.js instance from
 * the core authentication configuration in lib/auth.ts
 */

import NextAuth from "next-auth"
import { authConfig } from "@/lib/auth"

/**
 * Configured Auth.js utilities
 * @const {{ auth: Function, signIn: Function, signOut: Function }}
 * 
 * @property {Function} auth - Authentication middleware and session handler
 * @property {Function} signIn - Initiates sign in flow with specified provider
 * @property {Function} signOut - <PERSON>les user sign out
 * 
 * @example
 * ```ts
 * // Protect API route
 * export default auth((req) => {
 *   // Handle authenticated request
 * })
 * 
 * // Client-side authentication
 * const handleLogin = () => {
 *   signIn('credentials', {
 *     email,
 *     password,
 *     redirect: true,
 *     callbackUrl: '/dashboard'
 *   })
 * }
 * 
 * // Handle logout
 * const handleLogout = () => {
 *   signOut({
 *     redirect: true,
 *     callbackUrl: '/'
 *   })
 * }
 * ```
 */
export const { auth, signIn, signOut } = NextAuth(authConfig)