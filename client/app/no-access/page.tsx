"use client";

import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import Link from "next/link";

export default function NoAccessPage() {
  const { user } = useAuth();

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Access Denied</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            {user
              ? "You don't have permission to access this page."
              : "You need to be logged in to access this page."}
          </p>
          <div className="flex flex-col space-y-2">
            {user ? (
              <Link href="/dashboard">
                <Button className="w-full">Go to Dashboard</Button>
              </Link>
            ) : (
              <Link href="/auth/login">
                <Button className="w-full">Sign In</Button>
              </Link>
            )}
            <Link href="/">
              <Button variant="outline" className="w-full">
                Return Home
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}