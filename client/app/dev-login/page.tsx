/**
 * Development Login Page - DO NOT USE IN PRODUCTION
 * This page provides a simplified login flow for development.
 */

"use client";

import { useState } from "react";
import { signIn } from "next-auth/react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";

export default function DevLoginPage() {
  const [email, setEmail] = useState("<EMAIL>");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // First check if the user exists, create it if needed via API
      const checkResponse = await fetch(`/api/auth/dev-login?email=${encodeURIComponent(email)}&check=true`);
      const checkData = await checkResponse.json();
      
      if (!checkResponse.ok) {
        setError(checkData.error || "Error checking user");
        setLoading(false);
        return;
      }
      
      // Now use the Auth.js signIn function to properly sign in
      const response = await signIn("credentials", {
        email,
        password: "test123", // Default password for dev users
        callbackUrl: "/dashboard",
        redirect: true,
      });
      
      // NOTE: if redirect is true, we won't reach this code
      if (response?.error) {
        setError(response.error);
      }
    } catch (err) {
      setError(`Login failed: ${(err as Error).message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-muted/30">
      <Card className="w-[350px]">
        <CardHeader>
          <CardTitle className="text-2xl">Dev Login</CardTitle>
          <CardDescription>
            DEV ENVIRONMENT ONLY
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Input
                id="email"
                placeholder="Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mb-4"
              />
              {error && (
                <div className="text-sm text-red-500 mb-4">
                  {error}
                </div>
              )}
              <div className="text-xs text-muted-foreground">
                This login bypasses normal authentication.
                <br />
                DO NOT USE IN PRODUCTION!
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button className="w-full" onClick={handleLogin} disabled={loading}>
            {loading ? "Logging in..." : "Login (Development Only)"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}