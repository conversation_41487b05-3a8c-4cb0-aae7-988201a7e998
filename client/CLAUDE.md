# CLAUDE.md - Reference for shadcn-template

## Commands

- Build: `pnpm build` or `yarn build`
- Dev: `pnpm dev` or `yarn dev`
- Lint: `pnpm lint` or `yarn lint`
- Format: `pnpm format` or `yarn format`
- Type check: `pnpm type-check` or `yarn type-check`
- Test: `pnpm test` or `yarn test`
- Test single file: `pnpm test path/to/file.test.ts` or `yarn test path/to/file.test.ts`
- Test watch mode: `pnpm test:watch` or `yarn test:watch`

## Code Style Guidelines

- **Imports**: Use absolute imports with `@/` prefix (e.g., `import { db } from "@/lib/db"`)
- **Functions**: Use JSDoc comments for exported functions with `@param` and `@returns` tags
- **Error handling**: Return objects with `{ error: string }` or `{ success: string }` patterns
- **Naming**: Use camelCase for variables/functions, PascalCase for components/types
- **Types**: Use strict type checking, prefer explicit types over `any`
- **Components**: Use Typescript interfaces for component props
- **State management**: Redux Toolkit for global state, React hooks for local state
- **Styling**: Tailwind CSS with shadcn/ui components
- **Authentication**: Auth.js (NextAuth.js) with Prisma adapter

Always keeps in mind

### Core Technologies

- **Framework**: Next.js 15+
- **UI Library**: shadcn/ui 0.9.4+
- **Database**: PostgreSQL 17+ with Prisma ORM 6.2+
- **Authentication**: Auth.js (Beta)
- **Language**: TypeScript
- **Package Manager**: pnpm

### Frontend Technologies

- **UI Framework**: React 18+
- **Styling**: Tailwind CSS
- **Components**: shadcn/ui
- **Type Safety**: TypeScript

### Backend Technologies

- **ORM**: Prisma 6.2+
- **Database**: PostgreSQL 17+
- **API**: Next.js Route Handlers
- **Authentication**: Auth.js (Beta)

### Never ever use 'bcryptjs' package or any other package that may have edge runtime performance issue
