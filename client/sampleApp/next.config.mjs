/** @type {import('next').NextConfig} */
import NodePolyfillPlugin from 'node-polyfill-webpack-plugin';
import { NextFederationPlugin } from '@module-federation/nextjs-mf';

const nextConfig = {
  webpack: (config, { isServer }) => {
    // Handle Node.js built-in modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        os: false,
        path: false,
        stream: false,
        http: false,
        https: false,
      };

      // Add Module Federation plugin
      config.plugins.push(
        new NextFederationPlugin({
          name: 'adminApp',
          filename: 'static/chunks/remoteEntry.js',
          exposes: {
            './ApplicationsManager': './components/ApplicationsManager.tsx',
          },
          shared: {
            react: { singleton: true, eager: true, requiredVersion: '^18.0.0' },
            'react-dom': { singleton: true, eager: true, requiredVersion: '^18.0.0' },
          },
        })
      );
    }

    return config;
  },
};

export default nextConfig;