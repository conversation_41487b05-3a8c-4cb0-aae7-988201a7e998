import React from "react";
import ApplicationsManager from "@/components/ApplicationsManager";

export default function Home() {
  // This is a demo page to show the component working independently
  // When used as a micro-frontend, this component would be loaded remotely
  
  const demoApplications = [
    {
      id: "1",
      name: "user-management",
      displayName: "User Management",
      description: "Manage system users and permissions",
      path: "/admin/users",
      isActive: true,
      roles: "Admin, Manager",
      menuItemCount: 3,
      order: 1,
      createdAt: new Date().toISOString(),
    },
    {
      id: "2",
      name: "analytics-dashboard",
      displayName: "Analytics Dashboard",
      description: "View system analytics and statistics",
      path: "/admin/analytics",
      isActive: true,
      roles: "Admin, Manager, Analyst",
      menuItemCount: 5,
      order: 2,
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: "3",
      name: "content-manager",
      displayName: "Content Manager",
      description: "Manage website content and assets",
      path: "/admin/content",
      isActive: false,
      roles: "Admin, Content Editor",
      menuItemCount: 8,
      order: 3,
      createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ];

  return (
    <main className="flex min-h-screen flex-col items-center justify-between p-24">
      <div className="z-10 max-w-5xl w-full items-center justify-between font-mono text-sm">
        <h1 className="text-3xl font-bold mb-8 text-center">Admin Micro-Frontend</h1>
        <p className="mb-8 text-center">
          This is a standalone Next.js application that exposes components for the micro-frontend architecture.
          The ApplicationsManager component below is exposed via Module Federation.
        </p>
        
        <div className="bg-white p-6 rounded-lg shadow-md">
          <ApplicationsManager applications={demoApplications} />
        </div>
      </div>
    </main>
  );
}