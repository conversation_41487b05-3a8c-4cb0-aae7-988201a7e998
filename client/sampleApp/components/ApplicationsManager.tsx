'use client';

import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from './ui/table';
import { Badge } from './ui/badge';

interface Application {
  id: string;
  name: string;
  displayName: string;
  description: string;
  path: string;
  isActive: boolean;
  roles: string;
  menuItemCount: number;
  order: number;
  createdAt: string;
}

interface ApplicationsManagerProps {
  applications: Application[];
}

export default function ApplicationsManager({ applications }: ApplicationsManagerProps) {
  const [sortField, setSortField] = useState<keyof Application>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSort = (field: keyof Application) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedApplications = [...applications].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
      return sortDirection === 'asc'
        ? (aValue ? 1 : 0) - (bValue ? 1 : 0)
        : (bValue ? 1 : 0) - (aValue ? 1 : 0);
    }
    
    return 0;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  return (
    <div className="w-full">
      <div className="text-sm font-medium text-center text-gray-500 mb-4">
        This component is remotely loaded from the Admin Micro-frontend
      </div>
      
      <Table>
        <TableCaption>A list of applications in the system</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead 
              onClick={() => handleSort('name')}
              className="cursor-pointer hover:bg-gray-50"
            >
              Name {sortField === 'name' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
            <TableHead 
              onClick={() => handleSort('displayName')}
              className="cursor-pointer hover:bg-gray-50"
            >
              Display Name {sortField === 'displayName' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
            <TableHead>Description</TableHead>
            <TableHead 
              onClick={() => handleSort('path')}
              className="cursor-pointer hover:bg-gray-50"
            >
              Path {sortField === 'path' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
            <TableHead 
              onClick={() => handleSort('isActive')}
              className="cursor-pointer hover:bg-gray-50 text-center"
            >
              Status {sortField === 'isActive' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
            <TableHead>Roles</TableHead>
            <TableHead 
              onClick={() => handleSort('createdAt')}
              className="cursor-pointer hover:bg-gray-50 text-right"
            >
              Created {sortField === 'createdAt' && (sortDirection === 'asc' ? '↑' : '↓')}
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sortedApplications.map((app) => (
            <TableRow key={app.id}>
              <TableCell className="font-medium">{app.name}</TableCell>
              <TableCell>{app.displayName}</TableCell>
              <TableCell>{app.description}</TableCell>
              <TableCell>{app.path}</TableCell>
              <TableCell className="text-center">
                <Badge variant={app.isActive ? 'success' : 'destructive'}>
                  {app.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </TableCell>
              <TableCell>{app.roles}</TableCell>
              <TableCell className="text-right">{formatDate(app.createdAt)}</TableCell>
            </TableRow>
          ))}
          {applications.length === 0 && (
            <TableRow>
              <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                No applications found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
}