"use client";

import { useSession, signIn, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useState, useCallback } from "react";

/**
 * Custom hook for authentication functionality
 * Provides easy access to authentication state and methods
 */
export function useAuth() {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isAuthenticated = status === "authenticated";
  const isLoading = status === "loading";
  const user = session?.user;

  /**
   * Login with credentials
   */
  const login = useCallback(
    async (email: string, password: string) => {
      try {
        setLoading(true);
        setError(null);

        const result = await signIn("credentials", {
          email,
          password,
          redirect: false,
        });

        if (result?.error) {
          setError(
            result.error === "CredentialsSignin"
              ? "Invalid email or password"
              : result.error
          );
          return false;
        }

        router.push("/dashboard");
        router.refresh();
        return true;
      } catch (err: any) {
        setError(err.message || "An error occurred during login");
        return false;
      } finally {
        setLoading(false);
      }
    },
    [router]
  );

  /**
   * Register a new user
   */
  const register = useCallback(
    async (name: string, email: string, password: string) => {
      try {
        setLoading(true);
        setError(null);

        const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:4000';
        const response = await fetch(`${backendUrl}/api/auth/register`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ name, email, password }),
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Registration failed");
        }

        return true;
      } catch (err: any) {
        setError(err.message || "An error occurred during registration");
        return false;
      } finally {
        setLoading(false);
      }
    },
    []
  );

  /**
   * Logout the current user
   */
  const logout = useCallback(async () => {
    try {
      setLoading(true);
      await signOut({ redirect: false });
      router.push("/auth/login");
      router.refresh();
    } catch (err: any) {
      setError(err.message || "An error occurred during logout");
    } finally {
      setLoading(false);
    }
  }, [router]);

  /**
   * Check if user has a specific role
   */
  const hasRole = useCallback(
    (role: string) => {
      if (!user) return false;
      
      // Check in roleNames array first (preferred)
      if (user.roleNames && Array.isArray(user.roleNames)) {
        return user.roleNames.includes(role);
      }
      
      // Fallback to role string
      return user.role === role;
    },
    [user]
  );

  /**
   * Check if user has a specific permission
   */
  const hasPermission = useCallback(
    (permission: string) => {
      if (!user || !user.permissionNames || !Array.isArray(user.permissionNames)) {
        return false;
      }
      return user.permissionNames.includes(permission);
    },
    [user]
  );

  return {
    user,
    session,
    status,
    isAuthenticated,
    isLoading,
    loading,
    error,
    login,
    register,
    logout,
    hasRole,
    hasPermission,
    updateSession: update,
  };
}