# Use Node.js 18 as base image
FROM node:18-alpine

# Accept build arguments
ARG NODE_ENV=test

# Set working directory
WORKDIR /app

# Set environment variables
ENV NODE_ENV=$NODE_ENV

# Install dependencies
COPY package.json yarn.lock ./
RUN yarn install

# Copy source code and environment files
COPY . .

# Generate Prisma client
RUN yarn prisma generate

# Command to run tests
CMD ["yarn", "test"]
