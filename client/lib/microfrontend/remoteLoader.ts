import dynamic from 'next/dynamic';
import { ComponentType } from 'react';

/**
 * Interface for remote module configuration
 */
export interface RemoteModuleConfig {
  scope: string;         // The scope name defined in Module Federation (e.g., 'adminApp')
  module: string;        // The exposed module path (e.g., './AdminComponent')
  url?: string;          // Optional URL override (default: from environment variables)
  fallback?: React.ReactNode; // Optional fallback component while loading
  ssr?: boolean;         // Whether to render on server (default: false for remote modules)
}

/**
 * Loads a remote module dynamically using Module Federation
 * 
 * @param config Module configuration
 * @returns A dynamically loaded component
 */
export function loadRemoteModule<T = any>(config: RemoteModuleConfig): ComponentType<T> {
  const { scope, module, fallback, ssr = false } = config;
  
  return dynamic(
    // @ts-ignore - This is handled by Module Federation at runtime
    () => window[scope].get(module).then((factory: any) => factory()),
    {
      ssr,
      loading: fallback ? () => <>{fallback}</> : undefined,
    }
  );
}

/**
 * Helper function to load specific remote modules with predefined configurations
 * 
 * @param moduleName The module name to load
 * @param fallback Optional fallback component
 * @returns A dynamically loaded component
 */
export function loadRemoteComponent<T = any>(
  moduleName: string, 
  fallback?: React.ReactNode
): ComponentType<T> {
  // Map module names to their configurations
  const moduleConfigs: Record<string, RemoteModuleConfig> = {
    'adminDashboard': {
      scope: 'adminApp',
      module: './Dashboard',
      url: process.env.NEXT_PUBLIC_ADMIN_APP_URL,
      fallback,
    },
    'reports': {
      scope: 'reportsApp',
      module: './ReportSystem',
      url: process.env.NEXT_PUBLIC_REPORTS_APP_URL,
      fallback,
    },
    'analytics': {
      scope: 'analyticsApp',
      module: './AnalyticsDashboard',
      url: process.env.NEXT_PUBLIC_ANALYTICS_APP_URL,
      fallback,
    },
  };

  const config = moduleConfigs[moduleName];
  if (!config) {
    throw new Error(`Unknown remote module: ${moduleName}`);
  }

  return loadRemoteModule<T>(config);
}

/**
 * Utility to create a placeholder component while remote module is loading
 * 
 * @param title The title to display in the placeholder
 * @returns A React component
 */
export function createLoadingPlaceholder(title: string): React.ReactNode {
  return (
    <div className="border rounded-md p-4 w-full">
      <div className="h-6 bg-gray-200 rounded animate-pulse mb-2 w-1/2"></div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse w-5/6"></div>
        <div className="h-4 bg-gray-200 rounded animate-pulse w-4/6"></div>
      </div>
      <div className="mt-4 text-sm text-gray-500">Loading {title}...</div>
    </div>
  );
}