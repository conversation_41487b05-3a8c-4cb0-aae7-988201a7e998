import { MenuItem } from '@/types/menu';

/**
 * Fetches all menu items from the API
 * @param applicationId - Optional application ID to filter menu items
 * @returns Menu items
 */
export async function fetchMenuItems(applicationId?: string): Promise<MenuItem[]> {
  const url = applicationId 
    ? `/api/admin/menu?applicationId=${applicationId}`
    : '/api/admin/menu';
    
  const response = await fetch(url);
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to fetch menu items');
  }
  
  return response.json();
}

/**
 * Creates a new menu item
 * @param data - Menu item data
 * @returns Created menu item
 */
export async function createMenuItem(data: Partial<MenuItem>): Promise<MenuItem> {
  const response = await fetch('/api/admin/menu', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to create menu item');
  }
  
  return response.json();
}

/**
 * Updates an existing menu item
 * @param id - Menu item ID
 * @param data - Menu item data
 * @returns Updated menu item
 */
export async function updateMenuItem(id: string, data: Partial<MenuItem>): Promise<MenuItem> {
  const response = await fetch(`/api/admin/menu/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update menu item');
  }
  
  return response.json();
}

/**
 * Deletes a menu item
 * @param id - Menu item ID
 * @returns Success status
 */
export async function deleteMenuItem(id: string): Promise<{ success: boolean }> {
  const response = await fetch(`/api/admin/menu/${id}`, {
    method: 'DELETE',
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to delete menu item');
  }
  
  return response.json();
}

/**
 * Updates multiple menu items at once (for reordering)
 * @param items - Array of menu items with id and updated fields
 * @returns Success status
 */
export async function updateMenuItemsInBulk(items: Array<{ id: string; order?: number; parentId?: string | null }>): Promise<{ success: boolean }> {
  const response = await fetch('/api/admin/menu/bulk', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ items }),
  });
  
  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || 'Failed to update menu items');
  }
  
  return response.json();
}