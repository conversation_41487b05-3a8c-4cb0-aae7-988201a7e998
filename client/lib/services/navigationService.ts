import { auth } from "@/auth";
import { db } from "@/lib/db";

export type NavigationItem = {
  id: string;
  title: string;
  href: string;
  icon: string | null;
  applicationPath: string;
  parentId: string | null;
  children: NavigationItem[];
};

/**
 * Builds a hierarchical tree of navigation items
 * @param items Flat list of navigation items
 * @returns Hierarchical tree of navigation items
 */
function buildNavigationTree(items: any[]): NavigationItem[] {
  // Create a map of id to item with empty children array
  const itemMap: Record<string, NavigationItem> = {};
  items.forEach(item => {
    itemMap[item.id] = {
      ...item,
      children: []
    };
  });
  
  // Build the tree structure
  const rootItems: NavigationItem[] = [];
  
  items.forEach(item => {
    // If the item has a parentId and the parent exists in our map
    if (item.parentId && itemMap[item.parentId]) {
      // Add this item as a child of its parent
      itemMap[item.parentId].children.push(itemMap[item.id]);
    } else {
      // If no parentId or parent doesn't exist, it's a root item
      rootItems.push(itemMap[item.id]);
    }
  });
  
  // Sort the children by order
  const sortItems = (items: NavigationItem[]) => {
    items.sort((a, b) => a.order - b.order);
    items.forEach(item => {
      if (item.children.length > 0) {
        sortItems(item.children);
      }
    });
  };
  
  sortItems(rootItems);
  
  return rootItems;
}

/**
 * Fetch navigation menu items for the current user based on their roles and the section
 * @param section - The section to get menu items for (e.g., '/admin', '/dashboard')
 * @returns Hierarchical array of menu items
 */
export async function getNavigationMenuItems(section?: string) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return [];
    }
    
    // Get all applications the user has access to
    const userApplicationPaths = session.user.applicationPaths || [];
    
    // Build the query
    const query: any = {
      where: {
        application: {
          path: {
            in: userApplicationPaths
          }
        },
        isVisible: true
      }
    };
    
    // If section is provided, filter menu items for that section
    if (section) {
      query.where.path = {
        startsWith: section
      };
    }
    
    // Fetch all menu items for applications the user has access to with filtering
    const menuItems = await db.menuItem.findMany({
      ...query,
      orderBy: [
        {
          order: 'asc'
        }
      ],
      include: {
        application: true
      }
    });
    
    // Transform to navigation items with the necessary fields
    const navigationItems = menuItems.map(item => ({
      id: item.id,
      title: item.displayName,
      href: item.path,
      icon: item.icon,
      applicationPath: item.application.path,
      parentId: item.parentId,
      order: item.order
    }));
    
    // Build and return hierarchical navigation tree
    return buildNavigationTree(navigationItems);
  } catch (error) {
    console.error("Error fetching navigation menu items:", error);
    return [];
  }
}