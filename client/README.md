# Next.js 15+ Template with shadcn/ui

## Import initial Admin account

```
<EMAIL>
Admin@123
```

## Project Overview

A modern, full-stack application template built with Next.js 15+ and TypeScript, focusing on providing a complete authentication and authorization solution. This template integrates the latest technology stack, including Auth.js (NextAuth.js), Prisma ORM, and shadcn/ui, providing a solid foundation for developing secure, scalable web applications.

## 🌟 Key Features

### Authentication & Security

- 🔐 Complete user authentication system
  - Email/password registration and login
  - Social media login (Google, GitHub)
  - Password reset functionality
  - Email verification
  - Two-factor authentication (2FA)
- 🛡️ Advanced security measures
  - Rate limiting protection
  - CSRF protection
  - Password strength validation
  - Session management
  - JWT token management

### Technical Integration

- 📊 Prisma ORM 6.2+ Database Integration
  - Type-safe database operations
  - Auto-generated database client
  - Database migration management
- 🎨 Modern UI/UX
  - Tailwind CSS responsive design
  - shadcn/ui 0.9.4+ component library
  - Dark/light theme support
  - Adaptive layouts
- 🚀 Next.js 15+ Features
  - App Router
  - Server Components
  - Server Actions
  - Route Handlers

## 🏗️ System Architecture

### Frontend Architecture

1. **Routing**

   - Next.js 15+ App Router
   - Dynamic route management
   - Middleware authentication protection
   - Route group access control

2. **UI Architecture**
   - Component-based design
   - shadcn/ui integration
   - Theme system
   - Responsive layout

### Backend Architecture

1. **API Layer**

   - RESTful API design
   - API route handlers
   - Error handling middleware
   - Request validation

2. **Authentication Layer**

   - Auth.js (NextAuth.js) Beta integration
   - JWT handling
   - Session management
   - OAuth providers

3. **Data Layer**
   - Prisma 6.2+ Schema design
   - Database migrations
   - Query optimization
   - Relation handling

## 📁 Project Structure

```
.
├── app/                    # App Router (primary routing)
│   ├── (routes)/          # Route groups
│   │   ├── dashboard/     # Dashboard routes
│   │   └── profile/       # Profile routes
├── components/            # React components
│   ├── ui/               # UI components (shadcn/ui)
│   └── layout/           # Layout components
├── lib/                  # Utility functions
│   ├── utils/           # Helper functions
│   └── api/             # API utilities
├── hooks/               # Custom React hooks
├── types/               # TypeScript definitions
├── styles/              # Global CSS
├── public/             # Static assets
├── tests/              # Test files
└── middleware/         # Custom middleware
```

## 🛠️ Technology Stack

### Core Technologies

- **Framework**: Next.js 15+
- **UI Library**: shadcn/ui 0.9.4+
- **Database**: PostgreSQL 17+ with Prisma ORM 6.2+
- **Authentication**: Auth.js (Beta)
- **Language**: TypeScript
- **Package Manager**: yarn

### Frontend Technologies

- **UI Framework**: React 18+
- **Styling**: Tailwind CSS
- **Components**: shadcn/ui
- **Type Safety**: TypeScript

### Backend Technologies

- **ORM**: Prisma 6.2+
- **Database**: PostgreSQL 17+
- **API**: Next.js Route Handlers
- **Authentication**: Auth.js

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- yarn
- PostgreSQL 17+

### Installation Steps

1. Clone the repository:

   ```bash
   git clone <repository-url>
   cd shadcn-template
   ```

2. Install dependencies:

   ```bash
   yarn install
   ```

3. Environment setup:

   ```bash
   cp .env.example .env.local
   ```

   Fill in the required environment variables

4. Database setup:

   ```bash
   yarn prisma generate
   yarn prisma db push
   ```

5. Start development server:
   ```bash
   yarn dev
   ```

## ⚙️ Environment Variables

Required environment variables:

```env
# Application
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Database
DATABASE_URL="postgresql://user:password@localhost:5432/dbname"

# Authentication
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# OAuth Providers
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""
GITHUB_CLIENT_ID=""
GITHUB_CLIENT_SECRET=""
```

## 📚 Development Commands

- `yarn dev`: Start development server
- `yarn build`: Build production version
- `yarn start`: Start production server
- `yarn lint`: Run code linting
- `yarn test`: Run tests
- `yarn prisma studio`: Launch database management interface

## 🔧 Configuration

### Authentication

Configure in `auth.ts`:

- OAuth providers
- Session handling
- Callbacks
- Error handling

### UI Theme

Customize in `components.json` and `tailwind.config.js`:

- Color schemes
- Typography
- Spacing
- Component variants

## 📝 Documentation

### Database Schema

#### ER Diagram

```mermaid
erDiagram
    User ||--o{ Account : has
    User ||--o{ Session : has
    User ||--o{ TwoFactorConfirmation : has
    User ||--o{ LoginMethod : has
    User ||--o{ AuditLog : has

    User {
        string id PK
        string name "Optional"
        string email
        DateTime emailVerified "Optional"
        string image "Optional"
        string password "Optional"
        string role
        DateTime createdAt
        DateTime updatedAt
        int loginAttempts
        DateTime lastLoginAttempt "Optional"
        boolean isTwoFactorEnabled
    }

    Account {
        string userId FK
        string type
        string provider
        string providerAccountId
        string refresh_token "Optional"
        string access_token "Optional"
        int expires_at "Optional"
        string token_type "Optional"
        string scope "Optional"
        string id_token "Optional"
        DateTime createdAt
        DateTime updatedAt
    }

    Session {
        string id PK
        string sessionToken
        string userId FK
        DateTime expires
        DateTime lastActivity
        string userAgent "Optional"
        string ipAddress "Optional"
        DateTime createdAt
        DateTime updatedAt
    }

    TwoFactorConfirmation {
        string id PK
        string userId FK
        DateTime createdAt
    }

    LoginMethod {
        string id PK
        string userId FK
        string method
        DateTime createdAt
        DateTime updatedAt
    }

    VerificationToken {
        string id PK
        string email
        string token
        DateTime expires
    }

    PasswordResetToken {
        string id PK
        string email
        string token
        DateTime expires
    }

    TwoFactorToken {
        string id PK
        string email
        string token
        DateTime expires
    }

    AuditLog {
        string id PK
        string userId "Optional"
        string action
        string status
        DateTime timestamp
        string ipAddress "Optional"
        string userAgent "Optional"
        string targetUserId "Optional"
        string resourceId "Optional"
        string resourceType "Optional"
        string oldValue "Optional"
        string newValue "Optional"
        string reason "Optional"
        Json metadata "Optional"
    }
```

#### Table Descriptions

**User Table**

- Stores user authentication and profile information
- Primary Key: `id`
- Relationships:
  - One-to-Many with Account
  - One-to-Many with Session
  - One-to-One with TwoFactorConfirmation
  - One-to-Many with LoginMethod
  - One-to-Many with AuditLog

**Account Table**

- Stores linked authentication accounts (OAuth providers)
- Composite Primary Key: `[provider, providerAccountId]`
- Foreign Key: `userId` references User.id

**Session Table**

- Stores user session information
- Primary Key: `id`
- Unique: `sessionToken`
- Foreign Key: `userId` references User.id

**TwoFactorConfirmation Table**

- Stores two-factor authentication confirmation status
- Primary Key: `id`
- Unique: `userId`
- Foreign Key: `userId` references User.id

**LoginMethod Table**

- Stores user login methods
- Primary Key: `id`
- Foreign Key: `userId` references User.id

**VerificationToken Table**

- Stores email verification tokens
- Primary Key: `id`
- Unique: `token`
- Composite Unique: `[email, token]`

**PasswordResetToken Table**

- Stores password reset tokens
- Primary Key: `id`
- Unique: `token`
- Composite Unique: `[email, token]`

**TwoFactorToken Table**

- Stores two-factor authentication tokens
- Primary Key: `id`
- Unique: `token`
- Composite Unique: `[email, token]`

**AuditLog Table**

- Stores system audit logs
- Primary Key: `id`
- Foreign Key: `userId` references User.id
- Indexes:

  - `userId`
  - `action`
  - `timestamp`
  - `targetUserId`

- All TypeScript interfaces and types are documented using JSDoc
- Component props are documented with TypeScript interfaces
- API endpoints include OpenAPI documentation
- Database schema is documented in `schema.prisma`

## 🤝 Contributing

Please read our contributing guidelines before submitting pull requests.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
