/**
 * Authentication configuration for Next.js application
 * @module auth.config
 */

// Import core types/functions if needed, NextAuthConfig might not exist in v5 beta
// import { NextAuthConfig } from "next-auth";
import NextAuth from "next-auth/next"; // Import the main NextAuth object
import Credentials from "next-auth/providers/credentials"; // Keep these as they might still be correct for beta
import Google from "next-auth/providers/google";
import GitHub from "next-auth/providers/github";
import Microsoft from "next-auth/providers/microsoft";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { LoginSchema } from "@/schemas";
import { db } from "@/lib/db";
import { verifyPassword } from "@/lib/crypto";
import { User, UserStatus } from "@prisma/client";
// Reverted: import type { User, Session } from "@auth/core/types";
// Reverted: import type { JWT } from "@auth/core/jwt";
import { getUserRolesAndPermissions } from "@/lib/auth/roleService";


// Define AuthStatus directly as it was in the removed .d.ts file
export type AuthStatus = "pending" | "active" | "suspended" | "banned" | "deleted" | "inactive";

// Log all requests to debug session issues
console.log("Auth config loading...");

/**
 * Maps Prisma UserStatus to Next.js AuthStatus
 */
const mapStatus = (status: UserStatus): AuthStatus => {
  switch (status) {
    case "pending":
      return "pending";
    case "active":
      return "active";
    case "suspended":
      return "suspended";
    case "banned":
      return "banned";
    case "deleted":
      return "deleted";
    default:
      return "inactive";
  }
};

export const authConfig = {
  debug: true, // Enable debug mode
  trustHost: true, // Trust the host header during development
  adapter: PrismaAdapter(db) as any,
  providers: [
    // OAuth providers
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),

    GitHub({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),
    Microsoft({
      clientId: process.env.MICROSOFT_CLIENT_ID!,
      clientSecret: process.env.MICROSOFT_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),

    // Email/Password provider
    Credentials({
      id: "credentials",
      name: "Email & Password",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials: Record<string, unknown>): Promise<User | null> {
        try {
          console.log("Authorize function called with:", credentials);

          // Parse and validate credentials
          const validatedFields = LoginSchema.safeParse(credentials);
          if (!validatedFields.success) {
            console.log("Validation failed:", validatedFields.error);
            return null;
          }

          const { email, password } = validatedFields.data;
          console.log("Login attempt for:", email);

          // Forward credentials to Express.js backend for verification
          const backendUrl = process.env.BACKEND_URL || 'http://localhost:4000';
          const res = await fetch(`${backendUrl}/api/auth/login`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ email, password }),
            // credentials: 'include', // Removed - not strictly needed for login validation itself now
          });

          if (!res.ok) {
            console.log("Backend authentication failed:", await res.text());
            return null;
          }

          const userData = await res.json();
          console.log("Login successful for:", email);

          // Create user object with data from backend
          const safeUser: User = {
            id: userData.id,
            email: userData.email,
            name: userData.name ?? null,
            emailVerified: userData.emailVerified ?? null,
            image: userData.image ?? null,
            role: userData.role || 'user',
            status: userData.status || 'active',
            password: null, // Don't pass the password back
            isTwoFactorEnabled: userData.isTwoFactorEnabled ?? false,
            createdAt: new Date(userData.createdAt || Date.now()),
            updatedAt: new Date(userData.updatedAt || Date.now()),
            loginAttempts: userData.loginAttempts ?? 0,
            loginAttemptsResetAt: userData.loginAttemptsResetAt ? new Date(userData.loginAttemptsResetAt) : null,
            lastLoginAttempt: new Date(userData.lastLoginAttempt || Date.now()),
            lastSuccessfulLogin: new Date(userData.lastSuccessfulLogin || Date.now())
          };

          return safeUser;
        } catch (error) {
          console.error("Authorization error:", error);
          return null;
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, user }) {
      console.log("JWT callback called with user:", user ? user.id : "none", "and token:", token);
      if (user) {
        token.id = user.id;
        token.status = user.status;
        token.email = user.email;
        token.name = user.name ?? null;
        token.picture = user.image ?? null;

        try {
          // Get user roles and permissions
          const userRolesAndPermissions = await getUserRolesAndPermissions(user.id);

          // Simplify token data to reduce size
          // Store only role names and permission names instead of full objects
          token.roleNames = userRolesAndPermissions.roles.map(r => r.name);
          token.permissionNames = userRolesAndPermissions.permissions.map(p => p.name);
          token.applicationPaths = userRolesAndPermissions.applications.map(a => a.path);

          // For backward compatibility
          token.role = userRolesAndPermissions.roles.some(r => r.name === 'admin')
            ? 'admin'
            : 'user';

          console.log("JWT updated with user data and roles:", token);
        } catch (error) {
          console.error("Error getting user roles:", error);
          token.roleNames = [];
          token.permissionNames = [];
          token.applicationPaths = [];
          token.role = 'user'; // Default to user role
        }
      }
      return token;
    },
    async session({ session, token }) {
      console.log("Session callback called with token:", token);
      if (token) {
        session.user.id = token.id as string;
        session.user.status = token.status as UserStatus;
        session.user.email = token.email as string;
        session.user.name = token.name ?? null;
        session.user.image = token.picture ?? null;

        // For backward compatibility
        session.user.role = token.role as string;

        // Add simplified role data to session
        session.user.roleNames = token.roleNames || [];
        session.user.permissionNames = token.permissionNames || [];
        session.user.applicationPaths = token.applicationPaths || [];

        // For compatibility with existing code
        session.user.roles = session.user.roleNames.map(name => ({ name }));
        session.user.permissions = session.user.permissionNames.map(name => ({ name }));
        session.user.applications = session.user.applicationPaths.map(path => ({ path }));

        console.log("Session updated with token data:", session);
      }
      return session;
    }
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt" as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax" as const,
        path: "/",
        domain: process.env.COOKIE_DOMAIN || undefined,
        secure: process.env.NODE_ENV === "production"
      }
    }
  }
};
