{"name": "shadcn-template", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:seed": "yarn ts-node --esm prisma/seed.ts"}, "dependencies": {"@auth/core": "0.40.0", "@auth/prisma-adapter": "2.10.0", "@hookform/resolvers": "5.1.1", "@prisma/client": "6.11.1", "@radix-ui/react-avatar": "1.1.10", "@radix-ui/react-dialog": "1.1.14", "@radix-ui/react-dropdown-menu": "2.1.15", "@radix-ui/react-icons": "1.3.2", "@radix-ui/react-label": "2.1.7", "@radix-ui/react-navigation-menu": "1.2.13", "@radix-ui/react-popover": "1.1.14", "@radix-ui/react-progress": "1.1.7", "@radix-ui/react-select": "2.2.5", "@radix-ui/react-separator": "1.1.7", "@radix-ui/react-slot": "1.2.3", "@radix-ui/react-switch": "1.2.5", "@radix-ui/react-tabs": "1.1.12", "@radix-ui/react-toggle": "1.1.9", "@reduxjs/toolkit": "2.8.2", "@tanstack/react-table": "^8.21.3", "@types/zxcvbn": "4.4.5", "@vercel/functions": "2.2.3", "base64-arraybuffer": "1.0.2", "class-variance-authority": "0.7.1", "classnames": "2.5.1", "clsx": "2.1.1", "jose": "6.0.11", "lodash": "4.17.21", "lucide-react": "0.525.0", "nanoid": "5.1.5", "next": "15.3.5", "next-auth": "5.0.0-beta.25", "next-intl": "^4.3.4", "next-themes": "0.4.6", "react": "19.1.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "19.1.0", "react-hook-form": "7.60.0", "react-icons": "5.5.0", "react-redux": "9.2.0", "recharts": "^3.0.2", "redux-persist": "6.0.0", "resend": "^4.6.0", "sonner": "2.0.6", "tailwind-merge": "3.3.1", "tailwindcss-animate": "1.0.7", "uuid": "11.1.0", "zod": "3.25.74", "zxcvbn": "4.4.2"}, "devDependencies": {"@eslint/js": "^9.30.1", "@module-federation/nextjs-mf": "^8.8.32", "@module-federation/typescript": "^3.1.3", "@originjs/vite-plugin-federation": "^1.4.1", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "14.6.1", "@types/jest": "^30.0.0", "@types/lodash": "4.17.20", "@types/node": "24.0.10", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "8.35.1", "@typescript-eslint/parser": "8.35.1", "autoprefixer": "10.4.21", "eslint": "9.30.1", "eslint-config-next": "15.3.5", "eslint-config-prettier": "10.1.5", "eslint-plugin-import": "2.32.0", "eslint-plugin-jest": "29.0.1", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-next": "^0.0.0", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "globals": "^16.3.0", "husky": "9.1.7", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "lint-staged": "16.1.2", "node-polyfill-webpack-plugin": "^4.1.0", "null-loader": "^4.0.1", "postcss": "8.5.6", "prettier": "3.6.2", "prisma": "6.11.1", "resend": "^4.1.2", "tailwindcss": "4.1.11", "typescript": "5.8.3", "typescript-eslint": "^8.35.1", "webpack": "^5.99.9"}, "overrides": {"react-is": "19.0.0"}}