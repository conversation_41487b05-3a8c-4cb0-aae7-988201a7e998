import NodePolyfillPlugin from 'node-polyfill-webpack-plugin';
import { NextFederationPlugin } from '@module-federation/nextjs-mf';

/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Handle Node.js built-in modules
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        os: false,
        path: false,
        stream: false,
        http: false,
        https: false,
        zlib: false,
        console: false,
        diagnostics_channel: false,
      };

      // Add Module Federation plugin
      config.plugins.push(
        new NextFederationPlugin({
          name: 'hostApp',
          remotes: {
            // Define remote applications here
            adminApp: `adminApp@${process.env.NEXT_PUBLIC_ADMIN_APP_URL || 'http://localhost:3001'}/_next/static/chunks/remoteEntry.js`,
          },
          exposes: {
            // Components to expose to other micro-frontends
            './components/ui': './components/ui/index.ts',
            './components/admin': './components/admin/AdminHeader.tsx',
          },
          shared: {
            react: { singleton: true, eager: true, requiredVersion: '^18.0.0' },
            'react-dom': { singleton: true, eager: true, requiredVersion: '^18.0.0' },
            'next/router': { singleton: true, eager: true },
          },
        })
      );
    }

    // Exclude server-only modules from client bundles
    if (!isServer) {
      config.module.rules.push({
        test: /winston|winston-elasticsearch|@elastic\/elasticsearch/,
        use: 'null-loader',
      });
    }

    return config;
  },
};

export default nextConfig;
