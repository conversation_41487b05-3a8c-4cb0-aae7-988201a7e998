/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ["avatars.githubusercontent.com", "lh3.googleusercontent.com"],
  },
  // Temporarily disable TypeScript type checking in production build
  typescript: {
    // !! WARN !!
    // This allows production builds to successfully complete even if
    // your project has type errors.
    ignoreBuildErrors: true,
  },
  // Temporarily disable ESLint in production build
  eslint: {
    // !! WARN !!
    // This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  // Disable strict mode for this build
  reactStrictMode: false,
  // Skip certain optimizations in production build
  experimental: {
    optimizePackageImports: [],
    serverMinification: false,
    serverSourceMaps: true,
  },
  webpack: (config) => {
    config.resolve.fallback = {
      ...config.resolve.fallback,
      crypto: "crypto-browserify",
      stream: "stream-browserify",
      buffer: "buffer",
      util: "util",
      http: "stream-http",
      https: "https-browserify",
      url: false,
      querystring: false
    }
    return config
  }
}

module.exports = nextConfig
