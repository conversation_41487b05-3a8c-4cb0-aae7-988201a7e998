# Development stage
FROM node:18-alpine AS development
WORKDIR /app

RUN corepack enable
COPY package.json yarn.lock ./
RUN yarn install

COPY . .
RUN yarn build

EXPOSE 3000
CMD ["yarn", "dev"]

# Production stage
FROM node:18-alpine AS production
WORKDIR /app

RUN corepack enable
COPY package.json yarn.lock ./
RUN yarn install --production

COPY --from=development /app/.next ./.next
COPY --from=development /app/public ./public
COPY --from=development /app/package.json ./package.json

EXPOSE 3000
CMD ["yarn", "start"]
