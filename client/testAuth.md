 To test:
  1. Visit http://localhost:3000/api/auth/test-user to create a test user
  2. Try logging in with mailto:<EMAIL> and password123
  3. Check console logs for authentication flow details

  ---
  1. Visit http://localhost:3000/debug/session to see your current session
  state
  2. Try logging in with the test user (mailto:<EMAIL> /
  password123)
  3. After login attempt, check /debug/session again to see if a session was
   created
---
Here's how to use these tools to diagnose the issue:

  1. Visit http://localhost:3000/api/auth/test-user to ensure a test user
  exists
  2. Go to http://localhost:3000/debug/test-auth
  3. Try "Test Login Logic" first to verify the user credentials work
  correctly
  4. Then try "Test NextAuth Direct" to see what <PERSON>Auth is returning
  5. Finally, try "Use Standard Flow" to test the normal login process
  6. After each attempt, check http://localhost:3000/debug/session to see if
   a session was created

  These tools will help us pinpoint whether:
  - The database lookup is failing
  - The password verification is failing
  - The NextAuth callback is failing
  - The session creation is failing

  Each test provides different diagnostic information to identify the exact
  issue.

  ---
  Now restart your server to apply the new environment variables. The two
  keys you needed were:

  1. NEXTAUTH_SECRET matching your AUTH_SECRET
  2. NEXTAUTH_URL set to your local URL

  Try these steps after restarting:

  1. Go to /api/auth/test-user to create a test user
  2. Go to /debug/session
  3. Click the "Server Auth Action" button
  4. Check if the session is established