// actions/user/accessUserInfoAction.ts

import { db } from "@/lib/db";
import { Prisma } from "@prisma/client";
import { logger } from '@/lib/serverLogger';
import { AuthorizationError } from '@/lib/errors/AppError';

// Use experimental-edge runtime for better performance
export const runtime = "experimental-edge"

// Define a type for the user object
/**
 * @typedef {Object} UserWithLoginMethods
 * @description Extended user type that includes login methods
 */
type UserWithLoginMethods = Prisma.UserGetPayload<{
    include: { loginMethods: true }
}>;

/**
 * @interface AccessOptions
 * @description Configuration options for user data access
 * @property {boolean} [includeLoginMethods] - Whether to include login methods in the response
 * @property {boolean} [includeSensitiveData] - Whether to include sensitive data in the response
 */
interface AccessOptions {
    includeLoginMethods?: boolean;
    includeSensitiveData?: boolean;
}

const DEFAULT_OPTIONS: AccessOptions = {
    includeLoginMethods: false,
    includeSensitiveData: false,
};

/**
 * Validates if the requesting user has access to the target user's data
 * @param {Object} requestingUser - The user making the request
 * @param {string} requestingUser.id - ID of the requesting user
 * @param {string} requestingUser.role - Role of the requesting user
 * @param {string} targetUserId - ID of the user being accessed
 * @returns {boolean} - True if access is allowed, false otherwise
 * @description
 * Admins and SYSTEM users have full access. Regular users can only access their own data.
 */
const validateAccess = (requestingUser: { id: string; role: string }, targetUserId: string) => {
    if (requestingUser.role === 'admin' || requestingUser.role === 'SYSTEM') return true;
    return requestingUser.id === targetUserId;
};

/**
 * Sanitizes user data by removing sensitive fields unless explicitly requested
 * @param {UserWithLoginMethods} user - The user object to sanitize
 * @param {boolean} includeSensitiveData - Whether to include sensitive data
 * @returns {Partial<UserWithLoginMethods>} - Sanitized user object
 * @description
 * Removes sensitive fields like password unless includeSensitiveData is true.
 * Always keeps the password field but sets it to undefined when not including sensitive data.
 */
const sanitizeUserData = (user: UserWithLoginMethods, includeSensitiveData: boolean) => {
    // Always keep the password field for checking if it exists, but set it to undefined if not including sensitive data
    const sanitizedUser = {
        ...user,
        password: includeSensitiveData ? user.password : undefined,
    };
    return sanitizedUser;
};

// Cache user data with TTL
/**
 * @type {Map<string, {data: UserWithLoginMethods, timestamp: number}>}
 * @description Cache storage for user data with TTL to improve performance
 */
const userCache = new Map<string, {
    data: UserWithLoginMethods
    timestamp: number
}>()

/**
 * @constant {number}
 * @description Time-to-live duration for cached user data in milliseconds (5 minutes)
 */
const CACHE_TTL = 5 * 60 * 1000

/**
 * @description Periodic cleanup of expired cache entries
 * Runs every CACHE_TTL interval to remove stale data
 */
setInterval(() => {
    const now = Date.now()
    for (const [key, value] of userCache.entries()) {
        if (now - value.timestamp > CACHE_TTL) {
            userCache.delete(key)
        }
    }
}, CACHE_TTL)

/**
 * Retrieves a user by their email address with optional data inclusion
 * @async
 * @function getUserByEmail
 * @param {string} email - The email address to search for
 * @param {Object} requestingUser - The user making the request
 * @param {string} requestingUser.id - ID of the requesting user
 * @param {string} requestingUser.role - Role of the requesting user
 * @param {AccessOptions} [options=DEFAULT_OPTIONS] - Options for data inclusion
 * @returns {Promise<Partial<UserWithLoginMethods>|null>} - User object if found, null if not found
 * @throws {AuthorizationError} If unauthorized access is attempted
 * @throws {Error} If database connection fails
 * @description
 * Retrieves user data with caching support. Includes login methods and sensitive data based on options.
 * Validates access rights before returning data. Uses a 5-minute cache to improve performance.
 */
export const getUserByEmail = async (
    email: string,
    requestingUser: { id: string; role: string },
    options: AccessOptions = DEFAULT_OPTIONS
): Promise<Partial<UserWithLoginMethods> | null> => {
    if (!db) {
        logger.error('Database connection not established during user lookup by email');
        throw new Error('Database connection is not established');
    }

    try {
        logger.info('Looking up user by email', { email, requestingUserId: requestingUser.id });

        // Check cache first
        const cached = userCache.get(email)
        if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
            logger.debug("User info retrieved from cache", { email })
            return cached.data;
        }

        const user = await db.user.findUnique({
            where: { email },
            include: {
                loginMethods: options.includeLoginMethods
            }
        });

        if (!user) {
            logger.info('User not found by email', { email });
            return null;
        }

        if (!validateAccess(requestingUser, user.id)) {
            logger.warn('Unauthorized access attempt to user data', {
                requestingUserId: requestingUser.id,
                targetUserId: user.id
            });
            throw new AuthorizationError('Unauthorized access to user data');
        }

        // Cache the result
        userCache.set(email, {
            data: user,
            timestamp: Date.now()
        })

        const sanitizedUser = sanitizeUserData(user as UserWithLoginMethods, options.includeSensitiveData || false);
        logger.info('User found by email', { email, userId: user.id });
        return sanitizedUser;
    } catch (error) {
        const typedError = error as Error;
        logger.error('Error fetching user by email:', { error: typedError.message, stack: typedError.stack });
        if (error instanceof AuthorizationError) {
            throw error;
        }
        return null;
    }
};

/**
 * Retrieves a user by their ID with optional data inclusion
 * @async
 * @function getUserById
 * @param {string} id - The user ID to search for
 * @param {Object} requestingUser - The user making the request
 * @param {string} requestingUser.id - ID of the requesting user
 * @param {string} requestingUser.role - Role of the requesting user
 * @param {AccessOptions} [options=DEFAULT_OPTIONS] - Options for data inclusion
 * @returns {Promise<Partial<UserWithLoginMethods>|null>} - User object if found, null if not found
 * @throws {AuthorizationError} If unauthorized access is attempted
 * @throws {Error} If database connection fails
 * @description
 * Retrieves user data by ID with caching support. Includes login methods and sensitive data based on options.
 * Validates access rights before returning data. Uses a 5-minute cache to improve performance.
 */
export const getUserById = async (
    id: string,
    requestingUser: { id: string; role: string },
    options: AccessOptions = DEFAULT_OPTIONS
): Promise<Partial<UserWithLoginMethods> | null> => {
    if (!db) {
        logger.error('Database connection not established during user lookup by ID');
        throw new Error('Database connection is not established');
    }

    try {
        logger.info('Looking up user by ID', { id, requestingUserId: requestingUser.id });

        // Check cache first
        const cached = userCache.get(id)
        if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
            logger.debug("User info retrieved from cache", { id })
            return cached.data;
        }

        const user = await db.user.findUnique({
            where: { id },
            include: {
                loginMethods: options.includeLoginMethods
            }
        });

        if (!user) {
            logger.info('User not found by ID', { id });
            return null;
        }

        if (!validateAccess(requestingUser, user.id)) {
            logger.warn('Unauthorized access attempt to user data', {
                requestingUserId: requestingUser.id,
                targetUserId: user.id
            });
            throw new AuthorizationError('Unauthorized access to user data');
        }

        // Cache the result
        userCache.set(id, {
            data: user,
            timestamp: Date.now()
        })

        const sanitizedUser = sanitizeUserData(user as UserWithLoginMethods, options.includeSensitiveData || false);
        logger.info('User found by ID', { id });
        return sanitizedUser;
    } catch (error) {
        const typedError = error as Error;
        logger.error('Error fetching user by ID:', { error: typedError.message, stack: typedError.stack });
        if (error instanceof AuthorizationError) {
            throw error;
        }
        return null;
    }
};

/**
 * Updates user information with access control
 * @async
 * @function updateUser
 * @param {string} id - ID of the user to update
 * @param {Object} requestingUser - The user making the request
 * @param {string} requestingUser.id - ID of the requesting user
 * @param {string} requestingUser.role - Role of the requesting user
 * @param {Partial<Prisma.UserUpdateInput>} data - Data to update
 * @returns {Promise<Partial<UserWithLoginMethods>>} - Updated user object
 * @throws {AuthorizationError} If unauthorized access is attempted
 * @throws {Error} If database connection fails or update fails
 * @description
 * Updates user data after validating access rights. Updates cache after successful update.
 * Only allows users to update their own data unless they are admin/SYSTEM users.
 */
export const updateUser = async (
    id: string,
    requestingUser: { id: string; role: string },
    data: Partial<Prisma.UserUpdateInput>
): Promise<Partial<UserWithLoginMethods>> => {
    if (!db) {
        logger.error('Database connection not established during user update');
        throw new Error('Database connection is not established');
    }

    try {
        logger.info('Updating user', { id, requestingUserId: requestingUser.id });

        if (!validateAccess(requestingUser, id)) {
            logger.warn('Unauthorized update attempt to user data', {
                requestingUserId: requestingUser.id,
                targetUserId: id
            });
            throw new AuthorizationError('Unauthorized access to update user data');
        }

        const updatedUser = await db.user.update({
            where: { id },
            data,
            include: {
                loginMethods: true
            }
        });

        // Update cache
        userCache.set(id, {
            data: updatedUser,
            timestamp: Date.now()
        })

        const sanitizedUser = sanitizeUserData(updatedUser, false);
        logger.info('User updated successfully', { id });
        return sanitizedUser;
    } catch (error) {
        const typedError = error as Error;
        logger.error('Error updating user:', { error: typedError.message, stack: typedError.stack });
        if (error instanceof AuthorizationError) {
            throw error;
        }
        throw new Error('Failed to update user');
    }
};

/**
 * Deletes a user account with access control
 * @async
 * @function deleteUser
 * @param {string} id - ID of the user to delete
 * @param {Object} requestingUser - The user making the request
 * @param {string} requestingUser.id - ID of the requesting user
 * @param {string} requestingUser.role - Role of the requesting user
 * @returns {Promise<void>}
 * @throws {AuthorizationError} If unauthorized access is attempted
 * @throws {Error} If database connection fails or deletion fails
 * @description
 * Permanently removes a user account after validating access rights.
 * Only allows users to delete their own account unless they are admin/SYSTEM users.
 * Removes the user's data from both database and cache.
 */
export const deleteUser = async (
    id: string,
    requestingUser: { id: string; role: string }
): Promise<void> => {
    if (!db) {
        logger.error('Database connection not established during user deletion');
        throw new Error('Database connection is not established');
    }

    try {
        logger.info('Deleting user', { id, requestingUserId: requestingUser.id });

        if (!validateAccess(requestingUser, id)) {
            logger.warn('Unauthorized deletion attempt of user', {
                requestingUserId: requestingUser.id,
                targetUserId: id
            });
            throw new AuthorizationError('Unauthorized access to delete user');
        }

        await db.user.delete({
            where: { id }
        });

        // Remove from cache
        userCache.delete(id)

        logger.info('User deleted successfully', { id });
    } catch (error) {
        const typedError = error as Error;
        logger.error('Error deleting user:', { error: typedError.message, stack: typedError.stack });
        if (error instanceof AuthorizationError) {
            throw error;
        }
        throw new Error('Failed to delete user');
    }
};