"use server";

import { db } from "@/lib/db";
import { User } from "@prisma/client";
import { logger } from '@/lib/serverLogger';

/**
 * Retrieves a user by their unique identifier.
 *
 * @param {string} id - The unique identifier of the user to retrieve.
 * @returns {Promise<User | null>} A promise that resolves to the user object if found, null otherwise.
 * @throws {Error} Logs error if database query fails.
 */
export const getUserById = async (id: string): Promise<User | null> => {
  try {
    return await db.user.findUnique({
      where: { id },
      include: {
        accounts: true,
        sessions: true,
        twoFactorConfirmation: true,
        twoFactorTokens: true  // Changed from twoFactorToken to twoFactorTokens
      }
    });
  } catch (error) {
    const typedError = error as Error;
    logger.error('Error fetching user by ID:', { error: typedError.message, stack: typedError.stack });
    return null;
  }
};

/**
 * Retrieves a user by their email address.
 *
 * @param {string} email - The email address of the user to retrieve.
 * @returns {Promise<User | null>} A promise that resolves to the user object if found, null otherwise.
 * @throws {Error} Logs error if database query fails.
 */
export const getUserByEmail = async (email: string): Promise<User | null> => {
  try {
    return await db.user.findUnique({
      where: { email },
      include: {
        accounts: true,
        sessions: true,
        twoFactorConfirmation: true,
        twoFactorTokens: true  // Changed from twoFactorToken to twoFactorTokens
      }
    });
  } catch (error) {
    const typedError = error as Error;
    logger.error('Error fetching user by email:', { error: typedError.message, stack: typedError.stack });
    return null;
  }
};
