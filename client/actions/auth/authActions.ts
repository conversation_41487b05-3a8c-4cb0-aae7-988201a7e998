/**
 * @fileoverview Server actions for handling authentication operations
 * @module actions/auth/authActions
 */

// "use server"; // No longer a server action, becomes a client-side function calling the API

import * as z from "zod";
import { RegisterSchema } from "@/schemas/auth"; // Keep schema for potential client-side validation

// Base URL for the backend server API
const SERVER_API_URL = process.env.NEXT_PUBLIC_SERVER_API_URL || "http://localhost:3001/api"; // Default for dev

/**
 * Calls the backend API to register a new user.
 *
 * @param {z.infer<typeof RegisterSchema>} values - The registration form values
 * @returns {Promise<{error?: string; success?: string; details?: any}>} A promise resolving to the API response
 */
export const registerAction = async (values: z.infer<typeof RegisterSchema>) => {
  // Optional: Perform client-side validation first
  const validatedFields = RegisterSchema.safeParse(values);
  if (!validatedFields.success) {
    return { error: "Invalid fields!", details: validatedFields.error.flatten().fieldErrors };
  }

  try {
    const response = await fetch(`${SERVER_API_URL}/auth/register`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(validatedFields.data), // Send validated data
    });

    const result = await response.json();

    if (!response.ok) {
      // Use error message from API response if available, otherwise provide generic error
      return { error: result.error || `Registration failed (HTTP ${response.status})`, details: result.details };
    }

    return { success: result.success || "Registration successful!" }; // Use success message from API

  } catch (error) {
    console.error("Network or fetch error during registration:", error);
    return { error: "Registration failed due to a network error." };
  }
};

/**
 * Initiates the password reset process by generating and sending a reset token.
 *
 * @param {string} email - The email address of the user requesting password reset
 * @returns {Promise<{error: string} | {success: string}>} A promise that resolves to an object indicating success or error
 */
export const resetPassword = async (email: string) => {
  // Basic client-side check
  if (!email || typeof email !== 'string' || !email.includes('@')) {
     return { error: "Invalid email format." };
  }

  try {
    const response = await fetch(`${SERVER_API_URL}/auth/reset-password`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email }),
    });

    const result = await response.json(); // API should return { message: "..." } on success or error

    if (!response.ok) {
      return { error: result.error || `Password reset request failed (HTTP ${response.status})` };
    }

    // Use the message from the API (e.g., "If your email is registered...")
    return { success: result.message || "Password reset request processed." };

  } catch (error) {
    console.error("Network or fetch error during password reset request:", error);
    return { error: "Password reset request failed due to a network error." };
  }
};

/**
 * Updates user's password using a valid reset token.
 *
 * @param {string} token - The password reset token
 * @param {string} password - The new password
 * @returns {Promise<{error: string} | {success: string}>} A promise that resolves to an object indicating success or error
 */
export const newPasswordAction = async (
  token: string,
  password: string,
) => {
   // Basic client-side checks
  if (!token || typeof token !== 'string') {
     return { error: "Invalid token provided." };
  }
   if (!password || typeof password !== 'string' || password.length < 6) { // Example length check
     return { error: "Password must be at least 6 characters." };
  }

  try {
    const response = await fetch(`${SERVER_API_URL}/auth/new-password`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ token, password }),
    });

    const result = await response.json();

    if (!response.ok) {
      return { error: result.error || `Password update failed (HTTP ${response.status})` };
    }

    return { success: result.message || "Password updated successfully!" };

  } catch (error) {
    console.error("Network or fetch error during new password submission:", error);
    return { error: "Password update failed due to a network error." };
  }
};

/**
 * Resends the verification email to the specified email address.
 *
 * @param {string} email - The email address to send the verification email to
 * @returns {Promise<{error: string} | {success: string}>} A promise that resolves to an object indicating success or error
 */
export const resendVerificationEmail = async (email: string) => {
   // Basic client-side check
  if (!email || typeof email !== 'string' || !email.includes('@')) {
     return { error: "Invalid email format." };
  }

  try {
    const response = await fetch(`${SERVER_API_URL}/auth/resend-verification`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email }),
    });

    const result = await response.json();

    if (!response.ok) {
      return { error: result.error || `Failed to resend verification email (HTTP ${response.status})` };
    }

    // Use the message from the API (e.g., "If your email is registered...")
    return { success: result.success || "Verification email resent." };

  } catch (error) {
    console.error("Network or fetch error during resend verification email:", error);
    return { error: "Failed to resend verification email due to a network error." };
  }
};
