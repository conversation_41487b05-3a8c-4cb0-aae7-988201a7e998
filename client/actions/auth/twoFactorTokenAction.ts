// actions/auth/twoFactorTokenAction.ts
"use server";

import { db } from "@/lib/db";
import { TwoFactorToken } from "@prisma/client";
import { logger } from '@/lib/serverLogger';
import { generateToken } from "@/lib/crypto";

/**
 * Fetches a two-factor token by the token value.
 *
 * @param {string} token - The token to search for.
 * @returns {Promise<TwoFactorToken | null>} A promise that resolves to the two-factor token object if found, otherwise null.
 */
export const getTwoFactorTokenByToken = async (token: string): Promise<TwoFactorToken | null> => {
    try {
        return await db.twoFactorToken.findUnique({
            where: { token }
        });
    } catch (error) {
        const typedError = error as Error;
        logger.error('Error fetching two-factor token by token:', { error: typedError.message, stack: typedError.stack });
        return null;
    }
};

/**
 * Fetches a two-factor token by the user ID.
 *
 * @param {string} userId - The user ID to search for.
 * @returns {Promise<TwoFactorToken | null>} A promise that resolves to the two-factor token object if found, otherwise null.
 */
export const getTwoFactorTokenByUserId = async (userId: string): Promise<TwoFactorToken | null> => {
    try {
        return await db.twoFactorToken.findFirst({
            where: {
                user: {
                    id: userId
                }
            }
        });
    } catch (error) {
        const typedError = error as Error;
        logger.error('Error fetching two-factor token by user ID:', { error: typedError.message, stack: typedError.stack });
        return null;
    }
};

/**
 * Creates a new two-factor token for a user. If a token already exists for the user,
 * it will be deleted before creating a new one.
 *
 * @param {string} userId - The user ID associated with the token.
 * @returns {Promise<TwoFactorToken>} A promise that resolves to the created two-factor token object.
 * @throws {Error} If token creation fails.
 */
export const createTwoFactorToken = async (userId: string): Promise<TwoFactorToken> => {
    try {
        const token = generateToken();
        const expires = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes

        const existingToken = await getTwoFactorTokenByUserId(userId);
        if (existingToken) {
            await db.twoFactorToken.delete({
                where: { id: existingToken.id },
            });
        }

        return await db.twoFactorToken.create({
            data: {
                token,
                expires,
                user: {
                    connect: {
                        id: userId
                    }
                }
            },
        });
    } catch (error) {
        const typedError = error as Error;
        logger.error('Error creating two-factor token:', { error: typedError.message, stack: typedError.stack });
        throw new Error('Failed to create two-factor token');
    }
};

/**
 * Verifies a two-factor token for a user. The token must exist, belong to the specified user,
 * and not be expired.
 *
 * @param {string} userId - The user's ID to verify the token against
 * @param {string} token - The token string to verify
 * @returns {Promise<boolean>} A promise that resolves to true if the token is valid, false otherwise
 * @throws {Error} If token verification fails due to a database error
 */
export const verifyTwoFactorToken = async (userId: string, token: string): Promise<boolean> => {
    try {
        const twoFactorToken = await db.twoFactorToken.findFirst({
            where: {
                token,
                user: {
                    id: userId
                },
                expires: { gt: new Date() }
            }
        });

        if (!twoFactorToken) return false;

        // Delete the token after successful verification
        await db.twoFactorToken.delete({
            where: { id: twoFactorToken.id }
        });

        return true;
    } catch (error) {
        const typedError = error as Error;
        logger.error('Error verifying two-factor token:', { error: typedError.message, stack: typedError.stack });
        throw new Error('Failed to verify two-factor token');
    }
};
