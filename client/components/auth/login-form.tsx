/**
 * @fileoverview Login form component
 * @module components/auth/login-form
 * @description Provides a form for user authentication with
 * email/password and social login options
 */

"use client";

import { useState } from "react";
import { signIn } from "next-auth/react"; // Use client-side signIn
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { FormError } from "@/components/auth/common/FormError";
import { FormSuccess } from "@/components/auth/common/FormSuccess";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SocialButtons } from "@/components/auth/social-buttons";
import { Separator } from "@/components/ui/separator";
import { useSearchParams } from "next/navigation";

/**
 * LoginForm component for user authentication
 * @component
 * @description A form component that:
 * - Handles email/password authentication
 * - Provides social login options
 * - Shows loading states during authentication
 * - Displays success/error messages
 * - Redirects after successful login
 * 
 * @example
 * ```tsx
 * // Basic usage
 * <LoginForm />
 * 
 * // Within auth layout
 * <AuthLayout>
 *   <AuthHeader label="Welcome back" />
 *   <LoginForm />
 *   <BackButton
 *     href="/auth/register"
 *     label="Need an account?"
 *   />
 * </AuthLayout>
 * ```
 */
export function LoginForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/dashboard";
  const [error, setError] = useState<string | undefined>();
  const [success, setSuccess] = useState<string | undefined>();
  const [isPending, setIsPending] = useState(false);

  /**
   * Handles form submission for login
   * @async
   * @function
   * @param {React.FormEvent<HTMLFormElement>} e - Form submit event
   */
  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    try {
      setError(undefined);
      setSuccess(undefined);
      setIsPending(true);

      const formData = new FormData(e.currentTarget);
      const email = formData.get("email") as string;
      const password = formData.get("password") as string;

      console.log("Login attempt with:", { email, password: "***" });
      console.log("Callback URL:", callbackUrl);
      
      // Use the server API endpoint approach that works reliably
      try {
        const response = await fetch(
          `/api/auth/sign-in-test?email=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}&returnTo=${encodeURIComponent(callbackUrl)}`
        );
        const data = await response.json();
        console.log("Server auth response:", data);
        
        if (data.success) {
          setSuccess("Login successful!");
          toast.success("Login successful!");
          
          console.log("Login successful, redirecting to:", data.redirectTo);
          
          // Short delay to allow the session to be fully established
          setTimeout(() => {
            router.push(data.redirectTo);
            router.refresh();
          }, 500); // Increased delay for better reliability
        } else {
          setError(data.error || "Authentication failed");
          toast.error("Login failed: " + (data.error || "Unknown error"));
          console.error("Login failed:", data.error);
        }
      } catch (fetchError) {
        console.error("Fetch error:", fetchError);
        setError("Connection error. Please try again.");
        toast.error("Connection error");
      }
      
    } catch (error) {
      console.error("Login error:", error);
      setError("Something went wrong!");
      toast.error("Error during login");
    } finally {
      setIsPending(false);
    }
  };

  return (
    <div className="space-y-6">
      <form onSubmit={onSubmit} className="space-y-6">
        <div className="space-y-4">
          <Input
            name="email"
            type="email"
            placeholder="Email"
            disabled={isPending}
          />
          <Input
            name="password"
            type="password"
            placeholder="Password"
            disabled={isPending}
          />
        </div>
        <div className="flex items-center justify-end">
          <a 
            href="/auth/forgot-password" 
            className="text-sm text-primary hover:underline"
          >
            Forgot password?
          </a>
        </div>
        <FormError message={error} />
        <FormSuccess message={success} />
        <Button
          type="submit"
          className="w-full"
          disabled={isPending}
        >
          {isPending ? "Loading..." : "Login"}
        </Button>
      </form>
    </div>
  );
}
