"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  requiredPermission?: string;
  redirectTo?: string;
}

/**
 * Component that protects routes from unauthorized access
 * 
 * @param children - The content to render if authorized
 * @param requiredRole - Optional role requirement
 * @param requiredPermission - Optional permission requirement
 * @param redirectTo - Path to redirect unauthorized users (default: '/auth/login')
 */
export function ProtectedRoute({
  children,
  requiredRole,
  requiredPermission,
  redirectTo = "/auth/login",
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user, hasRole, hasPermission } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (isLoading) return;

    // Check authentication and authorization
    if (!isAuthenticated) {
      router.push(redirectTo);
      return;
    }

    if (requiredRole && !hasRole(requiredRole)) {
      router.push("/no-access");
      return;
    }

    if (requiredPermission && !hasPermission(requiredPermission)) {
      router.push("/no-access");
      return;
    }
  }, [isAuthenticated, isLoading, user, requiredRole, requiredPermission, router, redirectTo, hasRole, hasPermission]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect via useEffect
  }

  if ((requiredRole && !hasRole(requiredRole)) || 
      (requiredPermission && !hasPermission(requiredPermission))) {
    return null; // Will redirect via useEffect
  }

  return <>{children}</>;
}