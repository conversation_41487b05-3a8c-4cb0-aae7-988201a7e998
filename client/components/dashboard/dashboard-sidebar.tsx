"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { NavItem } from "./dashboard-nav-items";
import { ChevronDown, ChevronRight } from "lucide-react";

interface SidebarNavProps extends React.HTMLAttributes<HTMLElement> {
  items: NavItem[];
}

// Component for a single nav item (potentially with children)
function NavItemComponent({ item, level = 0, pathname }: { 
  item: NavItem;
  level?: number;
  pathname: string;
}) {
  const [expanded, setExpanded] = useState(false);
  const hasChildren = item.children && item.children.length > 0;
  const isActive = pathname === item.href || pathname.startsWith(item.href + '/');
  
  // Automatically expand items if they contain the active path
  const isActiveParent = hasChildren && item.children.some(
    child => pathname === child.href || pathname.startsWith(child.href + '/')
  );
  
  // Use effect to initialize expanded status based on active status
  useEffect(() => {
    if (isActiveParent) {
      setExpanded(true);
    }
  }, [isActiveParent]);
  
  return (
    <div>
      <div className="flex flex-col">
        <div 
          className={cn(
            "flex items-center rounded-md",
            isActive ? "bg-muted text-foreground" : "text-muted-foreground hover:text-foreground",
            "transition-colors"
          )}
          style={{ paddingLeft: `${level * 12 + 8}px` }}
        >
          {hasChildren && (
            <button
              onClick={() => setExpanded(!expanded)}
              className="p-2 rounded-md hover:bg-muted"
            >
              {expanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </button>
          )}
          
          <Link
            href={item.href}
            className={cn(
              "flex items-center py-2 px-3 rounded-md w-full",
              isActive ? "font-medium" : ""
            )}
          >
            <span className="mr-2">{item.icon}</span>
            <span>{item.title}</span>
          </Link>
        </div>
        
        {hasChildren && expanded && (
          <div className="mt-1">
            {item.children.map((child) => (
              <NavItemComponent 
                key={child.id} 
                item={child} 
                level={level + 1}
                pathname={pathname}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export function DashboardSidebar({ className, items, ...props }: SidebarNavProps) {
  const pathname = usePathname();

  return (
    <nav
      className={cn(
        "w-64 lg:w-72 flex-shrink-0 lg:flex-col lg:space-y-1 p-4 border-r h-full overflow-y-auto",
        className
      )}
      {...props}
    >
      <div className="space-y-1">
        {items.map((item) => (
          <NavItemComponent 
            key={item.id} 
            item={item}
            pathname={pathname}
          />
        ))}
      </div>
    </nav>
  );
}
