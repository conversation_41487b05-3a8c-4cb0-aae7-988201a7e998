'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/utils/cn';
import {
  LayoutDashboard,
  Users,
  Settings,
  ShieldCheck,
  Layers,
  Mail,
  FileText,
  BarChart3,
  Menu as MenuIcon,
  X
} from 'lucide-react';

interface MenuItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  submenu?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    title: 'Dashboard',
    href: '/admin',
    icon: <LayoutDashboard className="h-5 w-5" />,
  },
  {
    title: 'Users',
    href: '/admin/users',
    icon: <Users className="h-5 w-5" />,
  },
  {
    title: 'Applications',
    href: '/admin/applications',
    icon: <Layers className="h-5 w-5" />,
  },
  {
    title: 'Roles',
    href: '/admin/roles',
    icon: <ShieldCheck className="h-5 w-5" />,
  },
  {
    title: 'Reports',
    href: '/admin/reports',
    icon: <FileText className="h-5 w-5" />,
    submenu: [
      {
        title: 'Usage Reports',
        href: '/admin/reports/usage',
        icon: <BarChart3 className="h-4 w-4" />,
      },
      {
        title: 'User Reports',
        href: '/admin/reports/users',
        icon: <Users className="h-4 w-4" />,
      },
    ],
  },
  {
    title: 'Settings',
    href: '/admin/settings',
    icon: <Settings className="h-5 w-5" />,
  },
];

export function AdminMenu() {
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = React.useState(false);
  const [openSubmenu, setOpenSubmenu] = React.useState<string | null>(null);

  const toggleSubmenu = (title: string) => {
    setOpenSubmenu(prev => prev === title ? null : title);
  };

  const isActive = (href: string) => {
    return pathname === href || pathname?.startsWith(`${href}/`);
  };

  return (
    <>
      {/* Mobile menu button */}
      <div className="lg:hidden flex items-center p-4 border-b">
        <button 
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="text-gray-500 hover:text-gray-700 focus:outline-none"
        >
          {mobileMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <MenuIcon className="h-6 w-6" />
          )}
        </button>
        <span className="ml-3 font-medium">Admin Menu</span>
      </div>

      {/* Sidebar for desktop */}
      <div 
        className={cn(
          "z-20 w-64 bg-white border-r h-[calc(100vh-4rem)] flex-shrink-0 fixed inset-y-0 left-0 transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:h-auto",
          mobileMenuOpen ? "translate-x-0" : "-translate-x-full"
        )}
      >
        <div className="flex flex-col h-full">
          <div className="p-4 border-b lg:hidden">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold">Admin</h2>
              <button 
                onClick={() => setMobileMenuOpen(false)}
                className="text-gray-500 hover:text-gray-700 lg:hidden"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          <nav className="flex-1 overflow-y-auto py-4">
            <ul className="space-y-1 px-2">
              {menuItems.map((item) => (
                <li key={item.title}>
                  {item.submenu ? (
                    <div>
                      <button
                        onClick={() => toggleSubmenu(item.title)}
                        className={cn(
                          "flex items-center w-full px-3 py-2 text-sm rounded-md transition-colors",
                          isActive(item.href)
                            ? "bg-slate-100 text-slate-900 font-medium"
                            : "text-slate-700 hover:text-slate-900 hover:bg-slate-50"
                        )}
                      >
                        {item.icon}
                        <span className="ml-3 flex-1">{item.title}</span>
                        <svg
                          className={cn(
                            "h-4 w-4 transition-transform",
                            openSubmenu === item.title ? "rotate-180" : ""
                          )}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      </button>

                      {openSubmenu === item.title && (
                        <ul className="mt-1 pl-8 space-y-1">
                          {item.submenu.map((subitem) => (
                            <li key={subitem.title}>
                              <Link
                                href={subitem.href}
                                className={cn(
                                  "flex items-center px-3 py-2 text-sm rounded-md transition-colors",
                                  isActive(subitem.href)
                                    ? "bg-slate-100 text-slate-900 font-medium"
                                    : "text-slate-700 hover:text-slate-900 hover:bg-slate-50"
                                )}
                                onClick={() => setMobileMenuOpen(false)}
                              >
                                {subitem.icon}
                                <span className="ml-3">{subitem.title}</span>
                              </Link>
                            </li>
                          ))}
                        </ul>
                      )}
                    </div>
                  ) : (
                    <Link
                      href={item.href}
                      className={cn(
                        "flex items-center px-3 py-2 text-sm rounded-md transition-colors",
                        isActive(item.href)
                          ? "bg-slate-100 text-slate-900 font-medium"
                          : "text-slate-700 hover:text-slate-900 hover:bg-slate-50"
                      )}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      {item.icon}
                      <span className="ml-3">{item.title}</span>
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </nav>

          <div className="p-4 border-t">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-slate-200 flex items-center justify-center">
                <Users className="h-4 w-4 text-slate-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">Admin User</p>
                <p className="text-xs text-slate-500"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {mobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-10 lg:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}
    </>
  );
}