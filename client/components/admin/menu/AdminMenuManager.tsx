'use client';

import React, { useState, useEffect } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Plus, Trash2, Edit, ChevronDown, ChevronRight, GripVertical, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { 
  fetchMenuItems, 
  createMenuItem, 
  updateMenuItem, 
  deleteMenuItem, 
  updateMenuItemsInBulk 
} from '@/lib/services/menuService';
import { fetchApplications } from '@/lib/services/applicationService';
import { MenuItem, Application, MenuItemFormData } from '@/types/menu';

// List of available icons - this could be moved to a config file
const availableIcons = [
  'LayoutDashboard',
  'Users',
  'Settings',
  'Layers',
  'FileText',
  'BarChart',
  'User',
  'Shield',
  'Bell',
  'Home',
  'Menu',
  'ChevronRight',
  'ChevronDown',
];

// Drag and drop item types
const ItemTypes = {
  MENU_ITEM: 'menuItem',
};

// Draggable menu item component
const DraggableMenuItem = ({ 
  item, 
  level = 0, 
  onEdit, 
  onDelete, 
  onToggleActive, 
  onExpand, 
  isExpanded, 
  onMove 
}: { 
  item: MenuItem; 
  level?: number; 
  onEdit: (item: MenuItem) => void; 
  onDelete: (id: string) => void; 
  onToggleActive: (id: string, isVisible: boolean) => void; 
  onExpand: (id: string) => void; 
  isExpanded: boolean; 
  onMove: (dragId: string, hoverId: string) => void;
}) => {
  // Set up drag and drop
  const [{ isDragging }, drag, dragPreview] = useDrag(() => ({
    type: ItemTypes.MENU_ITEM,
    item: { id: item.id },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  const [{ isOver }, drop] = useDrop(() => ({
    accept: ItemTypes.MENU_ITEM,
    drop: (draggedItem: { id: string }) => {
      if (draggedItem.id !== item.id) {
        onMove(draggedItem.id, item.id);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }));

  // Combine drag and drop refs
  const ref = React.useRef<HTMLDivElement>(null);
  drag(drop(ref));

  const hasChildren = item.children && item.children.length > 0;

  return (
    <div ref={dragPreview} style={{ opacity: isDragging ? 0.5 : 1 }}>
      <div 
        ref={ref} 
        className={`flex items-center p-2 rounded-md mb-1 ${isOver ? 'bg-slate-100' : ''}`}
        style={{ marginLeft: `${level * 24}px` }}
      >
        <div className="flex-1 flex items-center">
          <div className="mr-2 cursor-move">
            <GripVertical size={16} className="text-gray-400" />
          </div>
          
          {hasChildren && (
            <button 
              className="mr-2 focus:outline-none"
              onClick={() => onExpand(item.id)}
            >
              {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
            </button>
          )}
          
          <div className={`flex-1 ${!item.isVisible ? 'text-gray-400' : ''}`}>
            {item.displayName} <span className="text-gray-400 text-xs">({item.path})</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Switch 
            checked={item.isVisible} 
            onCheckedChange={(checked) => onToggleActive(item.id, checked)}
          />
          
          <Button variant="ghost" size="sm" onClick={() => onEdit(item)}>
            <Edit size={16} />
          </Button>
          
          <Button variant="ghost" size="sm" onClick={() => onDelete(item.id)}>
            <Trash2 size={16} className="text-red-500" />
          </Button>
        </div>
      </div>
      
      {hasChildren && isExpanded && (
        <div>
          {item.children!.map((child) => (
            <DraggableMenuItem
              key={child.id}
              item={child}
              level={level + 1}
              onEdit={onEdit}
              onDelete={onDelete}
              onToggleActive={onToggleActive}
              onExpand={onExpand}
              isExpanded={isExpanded}
              onMove={onMove}
            />
          ))}
        </div>
      )}
    </div>
  );
};

// Form for adding/editing menu items
const MenuItemForm = ({ 
  item, 
  onSave, 
  onCancel, 
  menuItems,
  applications 
}: { 
  item?: MenuItem; 
  onSave: (item: MenuItemFormData) => void; 
  onCancel: () => void; 
  menuItems: MenuItem[];
  applications: Application[];
}) => {
  const [formData, setFormData] = useState<Partial<MenuItemFormData>>(
    item ? {
      name: item.name,
      displayName: item.displayName,
      path: item.path,
      icon: item.icon || undefined,
      isVisible: item.isVisible,
      parentId: item.parentId,
      applicationId: item.applicationId,
      order: item.order
    } : {
      name: '',
      displayName: '',
      path: '',
      icon: 'LayoutDashboard',
      isVisible: true,
      parentId: null,
      applicationId: applications.length > 0 ? applications[0].id : '',
      order: 0
    }
  );

  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name || !formData.displayName || !formData.path || !formData.applicationId) {
      toast.error('Name, display name, path and application are required');
      return;
    }
    
    onSave(formData as MenuItemFormData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="name">Menu Item Name (Internal)</Label>
        <Input
          id="name"
          value={formData.name || ''}
          onChange={(e) => handleChange('name', e.target.value)}
          placeholder="dashboard"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="displayName">Display Name</Label>
        <Input
          id="displayName"
          value={formData.displayName || ''}
          onChange={(e) => handleChange('displayName', e.target.value)}
          placeholder="Dashboard"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="path">Path</Label>
        <Input
          id="path"
          value={formData.path || ''}
          onChange={(e) => handleChange('path', e.target.value)}
          placeholder="/dashboard"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="icon">Icon</Label>
        <Select
          value={formData.icon || ''}
          onValueChange={(value) => handleChange('icon', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select icon" />
          </SelectTrigger>
          <SelectContent>
            {availableIcons.map((icon) => (
              <SelectItem key={icon} value={icon}>
                {icon}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="application">Application</Label>
        <Select
          value={formData.applicationId || ''}
          onValueChange={(value) => handleChange('applicationId', value)}
          required
        >
          <SelectTrigger>
            <SelectValue placeholder="Select application" />
          </SelectTrigger>
          <SelectContent>
            {applications.map((app) => (
              <SelectItem key={app.id} value={app.id}>
                {app.displayName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="parent">Parent Menu</Label>
        <Select
          value={formData.parentId || 'null'}
          onValueChange={(value) => handleChange('parentId', value === 'null' ? null : value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="None (Top Level)" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="null">None (Top Level)</SelectItem>
            {menuItems
              .filter((menuItem) => menuItem.id !== item?.id && !menuItem.parentId)
              .map((menuItem) => (
                <SelectItem key={menuItem.id} value={menuItem.id}>
                  {menuItem.displayName}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="order">Display Order</Label>
        <Input
          id="order"
          type="number"
          min="0"
          value={formData.order?.toString() || '0'}
          onChange={(e) => handleChange('order', parseInt(e.target.value))}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Label htmlFor="visible">Visible</Label>
        <Switch
          id="visible"
          checked={formData.isVisible}
          onCheckedChange={(checked) => handleChange('isVisible', checked)}
        />
      </div>

      <div className="flex justify-end space-x-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">{item ? 'Update' : 'Add'} Menu Item</Button>
      </div>
    </form>
  );
};

// Build the tree structure
const buildMenuTree = (items: MenuItem[]) => {
  const itemMap: Record<string, MenuItem> = {};
  const rootItems: MenuItem[] = [];

  // Create a map of all items
  items.forEach((item) => {
    itemMap[item.id] = { ...item, children: [] };
  });

  // Build the tree structure
  items.forEach((item) => {
    if (item.parentId && itemMap[item.parentId]) {
      if (!itemMap[item.parentId].children) {
        itemMap[item.parentId].children = [];
      }
      itemMap[item.parentId].children!.push(itemMap[item.id]);
    } else {
      rootItems.push(itemMap[item.id]);
    }
  });

  // Sort root items
  rootItems.sort((a, b) => a.order - b.order);

  // Sort children
  rootItems.forEach((item) => {
    if (item.children) {
      item.children.sort((a, b) => a.order - b.order);
    }
  });

  return rootItems;
};

// Main component
export function AdminMenuManager() {
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [applications, setApplications] = useState<Application[]>([]);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [editingItem, setEditingItem] = useState<MenuItem | undefined>();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [selectedApp, setSelectedApp] = useState<string | null>(null);

  // Fetch menu items and applications on mount
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // Load applications first
        const apps = await fetchApplications();
        setApplications(apps);
        
        // Set first app as selected if none is selected
        if (apps.length > 0 && !selectedApp) {
          setSelectedApp(apps[0].id);
        }
        
        // Load menu items for selected app or all
        const items = await fetchMenuItems(selectedApp || undefined);
        setMenuItems(items);
        
        // Load expanded items from localStorage
        if (typeof window !== 'undefined') {
          const savedExpandedItems = localStorage.getItem('adminExpandedMenuItems');
          if (savedExpandedItems) {
            setExpandedItems(new Set(JSON.parse(savedExpandedItems)));
          }
        }
      } catch (error) {
        console.error("Error loading data:", error);
        toast.error("Failed to load menu data");
      } finally {
        setLoading(false);
      }
    };
    
    loadData();
  }, [selectedApp]);
  
  // Save expanded items to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('adminExpandedMenuItems', JSON.stringify(Array.from(expandedItems)));
    }
  }, [expandedItems]);

  // Build menu tree
  const menuTree = buildMenuTree(menuItems);

  // Toggle item expanded state
  const handleExpand = (id: string) => {
    setExpandedItems((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Delete menu item
  const handleDelete = async (id: string) => {
    // Ask for confirmation
    if (!confirm('Are you sure you want to delete this menu item? This action cannot be undone.')) {
      return;
    }
    
    try {
      await deleteMenuItem(id);
      
      // Remove the item and its children from state
      const newItems = menuItems.filter(
        (item) => item.id !== id && item.parentId !== id
      );
      setMenuItems(newItems);
      toast.success('Menu item deleted');
    } catch (error) {
      console.error("Error deleting menu item:", error);
      toast.error("Failed to delete menu item");
    }
  };

  // Toggle menu item visible state
  const handleToggleActive = async (id: string, isVisible: boolean) => {
    try {
      await updateMenuItem(id, { isVisible });
      
      // Update state
      setMenuItems((prev) =>
        prev.map((item) => (item.id === id ? { ...item, isVisible } : item))
      );
      toast.success(`Menu item ${isVisible ? 'shown' : 'hidden'}`);
    } catch (error) {
      console.error("Error updating menu item visibility:", error);
      toast.error("Failed to update menu item visibility");
    }
  };

  // Edit menu item
  const handleEdit = (item: MenuItem) => {
    setEditingItem(item);
    setIsDialogOpen(true);
  };

  // Save menu item (add or update)
  const handleSave = async (formData: MenuItemFormData) => {
    try {
      if (editingItem) {
        // Update existing item
        const updatedItem = await updateMenuItem(editingItem.id, formData);
        
        // Update state
        setMenuItems((prev) =>
          prev.map((prevItem) => (prevItem.id === updatedItem.id ? updatedItem : prevItem))
        );
        toast.success('Menu item updated');
      } else {
        // Add new item
        const newItem = await createMenuItem(formData);
        setMenuItems((prev) => [...prev, newItem]);
        toast.success('Menu item added');
      }
      setIsDialogOpen(false);
      setEditingItem(undefined);
    } catch (error) {
      console.error("Error saving menu item:", error);
      toast.error("Failed to save menu item");
    }
  };

  // Move menu item (drag and drop)
  const handleMove = async (dragId: string, hoverId: string) => {
    // Find the items
    const dragItem = menuItems.find((item) => item.id === dragId);
    const hoverItem = menuItems.find((item) => item.id === hoverId);
    
    if (!dragItem || !hoverItem) return;
    
    // Create updated items for API
    const updates = [
      { id: dragId, order: hoverItem.order, parentId: hoverItem.parentId },
      { id: hoverId, order: dragItem.order }
    ];
    
    try {
      // Update the API
      await updateMenuItemsInBulk(updates);
      
      // Update the state
      const newItems = menuItems.map((item) => {
        if (item.id === dragId) {
          return { ...item, order: hoverItem.order, parentId: hoverItem.parentId };
        }
        if (item.id === hoverId) {
          return { ...item, order: dragItem.order };
        }
        return item;
      });
      
      setMenuItems(newItems);
      toast.success('Menu order updated');
    } catch (error) {
      console.error("Error reordering menu items:", error);
      toast.error("Failed to update menu order");
    }
  };

  // Add new menu item
  const handleAddNew = () => {
    setEditingItem(undefined);
    setIsDialogOpen(true);
  };
  
  // Filter by application
  const handleAppChange = (appId: string) => {
    setSelectedApp(appId === 'all' ? null : appId);
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Menu Structure</h3>
          <div className="flex gap-2">
            {applications.length > 0 && (
              <Select
                value={selectedApp || 'all'}
                onValueChange={handleAppChange}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select Application" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Applications</SelectItem>
                  {applications.map((app) => (
                    <SelectItem key={app.id} value={app.id}>
                      {app.displayName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button onClick={handleAddNew}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Menu Item
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>
                    {editingItem ? 'Edit Menu Item' : 'Add Menu Item'}
                  </DialogTitle>
                </DialogHeader>
                {applications.length > 0 ? (
                  <MenuItemForm
                    item={editingItem}
                    onSave={handleSave}
                    onCancel={() => setIsDialogOpen(false)}
                    menuItems={menuItems}
                    applications={applications}
                  />
                ) : (
                  <div className="text-center py-4">
                    No applications found. Please create an application first.
                  </div>
                )}
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <div className="border rounded-md p-4 min-h-[400px]">
          {loading ? (
            <div className="flex items-center justify-center h-60">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : menuTree.length > 0 ? (
            <div className="space-y-1">
              {menuTree.map((item) => (
                <DraggableMenuItem
                  key={item.id}
                  item={item}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  onToggleActive={handleToggleActive}
                  onExpand={handleExpand}
                  isExpanded={expandedItems.has(item.id)}
                  onMove={handleMove}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No menu items found. Add your first menu item to get started.
            </div>
          )}
        </div>
      </div>
    </DndProvider>
  );
}