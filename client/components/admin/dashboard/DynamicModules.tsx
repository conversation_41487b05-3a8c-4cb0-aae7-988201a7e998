'use client';

import dynamic from 'next/dynamic';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

// Example of dynamically importing micro-frontend components
// In a real implementation, these would point to remote modules from other apps
export const DataVisualizationModule = dynamic(
  () => import('@/components/admin/dashboard/Overview').then(mod => mod.Overview),
  {
    ssr: false,
    loading: () => (
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Loading Remote Module...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-24 animate-pulse bg-gray-200 rounded-md"></div>
        </CardContent>
      </Card>
    )
  }
);

// This would normally be imported from another micro-frontend
export const ReportsModule = dynamic(
  () => import('@/components/admin/dashboard/RecentActivity').then(mod => mod.RecentActivity),
  { 
    ssr: false,
    loading: () => (
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Loading Reports Module...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-24 animate-pulse bg-gray-200 rounded-md"></div>
        </CardContent>
      </Card>
    )
  }
);