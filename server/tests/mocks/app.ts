import express from 'express';
import cookieParser from 'cookie-parser';
import { corsMiddleware, sessionMiddleware } from './session';
import { authRouter } from './auth-routes';

// Create a mock Express app for testing
const app = express();

app.use(corsMiddleware);
app.use(express.json());
app.use(cookieParser());
app.use(sessionMiddleware);

// Health check endpoint
app.get('/api/health', (req, res) => {
  // Set a session cookie for the API test
  res.cookie('sid', 'test-session-id', {
    httpOnly: true,
    secure: false,
    sameSite: 'lax'
  });
  res.status(200).json({ status: 'ok' });
});

// Use both /auth and /api/auth routes to support different test cases
app.use('/auth', authRouter);
app.use('/api/auth', authRouter);

export { app };