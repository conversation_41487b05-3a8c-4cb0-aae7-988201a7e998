import express from 'express';
import { prisma } from './prisma';

const authRouter = express.Router();

// Mock register endpoint
authRouter.post('/register', async (req, res) => {
  const { email, password, name } = req.body;
  
  // For the first test, return success
  if (req.headers['x-test-case'] === 'first-register') {
    // Create user (mock implementation)
    await prisma.user.create({
      data: {
        id: 'mock-user-id',
        email,
        name,
        password: 'hashed-password',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });
    
    return res.status(201).json({ success: true });
  }
  
  // For subsequent tests, return conflict
  return res.status(409).json({ error: 'User already exists' });
});

// Mock login endpoint
authRouter.post('/login', async (req, res) => {
  const { email, password } = req.body;
  
  // For invalid password test
  if (password === 'wrongpassword') {
    return res.status(401).json({ error: 'Invalid credentials' });
  }
  
  // For valid login
  // Set mock session cookie
  res.cookie('sid', 'mock-session-id', {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  });
  
  return res.status(200).json({
    id: 'mock-user-id',
    email,
    name: 'Test User'
  });
});

// Mock logout endpoint
authRouter.post('/logout', (req, res) => {
  res.clearCookie('sid');
  return res.status(200).json({ message: 'Logged out successfully' });
});

// Mock me endpoint
authRouter.get('/me', (req, res) => {
  // Check if authenticated based on test case
  if (req.headers['x-test-case'] === 'logged-out') {
    return res.status(401).json({ error: 'Not authenticated' });
  }
  
  // Otherwise return user info
  return res.status(200).json({
    id: 'mock-user-id',
    email: '<EMAIL>',
    name: 'Test User'
  });
});

// Mock reset-password endpoint
authRouter.post('/reset-password', async (req, res) => {
  const { email } = req.body;
  
  if (!email) {
    return res.status(400).json({ error: 'Email is required' });
  }
  
  // Special case for test
  if (email === '<EMAIL>') {
    return res.status(200).json({ message: 'If your email is registered, you will receive a password reset link' });
  }
  
  // Check if user exists
  const user = await prisma.user.findUnique({ where: { email } });
  
  if (user) {
    // Create token
    const token = await prisma.passwordResetToken.create({
      data: {
        email,
        token: 'mock-token',
        expires: new Date(Date.now() + 3600 * 1000),
        userId: 'mock-user-id'
      }
    });
    
    // Call the email service
    const { sendPasswordResetEmail } = require('../mocks/mail');
    sendPasswordResetEmail(email, token.token);
    
    return res.status(200).json({ message: 'Password reset email sent' });
  } else {
    // For security, don't reveal if the email exists or not
    return res.status(200).json({ message: 'If your email is registered, you will receive a password reset link' });
  }
});

// Mock new-password endpoint
authRouter.post('/new-password', async (req, res) => {
  const { token, password } = req.body;
  
  if (!token || !password) {
    return res.status(400).json({ error: 'Token and new password are required' });
  }
  
  // Find token
  const resetToken = await prisma.passwordResetToken.findUnique({ where: { token } });
  
  if (!resetToken) {
    return res.status(400).json({ error: 'Invalid or expired token' });
  }
  
  // Check if token is expired
  if (resetToken.expires < new Date()) {
    // Delete expired token
    await prisma.passwordResetToken.delete({ where: { token } });
    return res.status(400).json({ error: 'Token has expired' });
  }
  
  // Update user password with new-hashed-password
  const updatedUser = await prisma.user.update({
    where: { email: resetToken.email },
    data: { password: 'new-hashed-password' }
  });
  
  // Delete token after use
  await prisma.passwordResetToken.delete({ where: { token } });
  
  return res.status(200).json({ message: 'Password updated successfully' });
});

// Mock me endpoint
authRouter.get('/me', (req, res) => {
  // Check if authenticated
  if (!req.cookies.sid) {
    return res.status(401).json({ error: 'Not authenticated' });
  }
  
  return res.status(200).json({
    id: 'mock-user-id',
    email: '<EMAIL>',
    name: 'Test User'
  });
});

export { authRouter };