// Mock Prisma client for tests
export const prisma = {
  user: {
    findUnique: jest.fn().mockImplementation(({ where }) => {
      if (where.email === '<EMAIL>') {
        return null;
      }
      return {
        id: 'mock-user-id',
        email: where.email,
        name: 'Test User',
        password: 'hashed-password',
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }),
    findFirst: jest.fn(),
    create: jest.fn().mockImplementation(({ data }) => {
      return {
        id: 'mock-user-id',
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }),
    update: jest.fn().mockImplementation(({ where, data }) => {
      // Always return with new-hashed-password for password reset tests
      return {
        id: 'mock-user-id',
        email: where.email,
        name: 'Test User',
        password: 'new-hashed-password',
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
  session: {
    create: jest.fn(),
    findUnique: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
  verificationToken: {
    create: jest.fn(),
    findUnique: jest.fn(),
    delete: jest.fn(),
    deleteMany: jest.fn(),
  },
  passwordResetToken: {
    create: jest.fn().mockImplementation(({ data }) => {
      return {
        id: 'mock-token-id',
        ...data,
        userId: 'mock-user-id'
      };
    }),
    findFirst: jest.fn().mockImplementation(({ where }) => {
      // Special case for non-existent email test
      if (where.email === '<EMAIL>') {
        return null;
      }
      
      // Normal case
      return {
        id: 'mock-token-id',
        email: where.email,
        token: 'mock-token',
        expires: new Date(Date.now() + 3600 * 1000),
        userId: 'mock-user-id'
      };
    }),
    findUnique: jest.fn().mockImplementation(({ where }) => {
      if (where.token === 'invalid-token-string') {
        return null;
      }
      
      if (where.token === 'expired-token') {
        return {
          id: 'expired-token-id',
          email: '<EMAIL>',
          token: 'expired-token',
          expires: new Date(Date.now() - 3600 * 1000), // Expired 1 hour ago
          userId: 'mock-user-id'
        };
      }
      
      return {
        id: 'mock-token-id',
        email: '<EMAIL>',
        token: where.token,
        expires: new Date(Date.now() + 3600 * 1000), // Expires in 1 hour
        userId: 'mock-user-id'
      };
    }),
    delete: jest.fn().mockImplementation(({ where }) => {
      // Return null to simulate successful deletion
      return null;
    }),
    deleteMany: jest.fn(),
  },
  $connect: jest.fn(),
  $disconnect: jest.fn(),
};

export default prisma;