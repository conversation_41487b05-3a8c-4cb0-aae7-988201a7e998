import express from 'express';
import cors from 'cors';

// Mock session middleware
export const sessionMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  // Add session data to request
  (req as any).session = {
    user: null
  };
  
  // Add session methods
  (req as any).session.save = (callback: () => void) => {
    if (callback) callback();
  };
  
  (req as any).session.destroy = (callback: () => void) => {
    (req as any).session.user = null;
    if (callback) callback();
  };
  
  next();
};

// Mock CORS middleware
export const corsMiddleware = cors({
  origin: process.env.CLIENT_URL || 'http://localhost:3000',
  credentials: true
});