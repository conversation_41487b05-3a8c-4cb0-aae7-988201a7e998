import request from 'supertest';
import { app } from './mocks/app';
import { prisma } from './mocks/prisma';

describe('API Tests', () => {
  beforeAll(async () => {
    await prisma.$connect();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  test('GET /api/health should return 200', async () => {
    const response = await request(app).get('/api/health');
    expect(response.status).toBe(200);
    expect(response.body).toEqual({ status: 'ok' });
  });

  test('Session cookie should be set on first request', async () => {
    const response = await request(app).get('/api/health');
    expect(response.headers['set-cookie']).toBeDefined();
    expect(response.headers['set-cookie'][0]).toMatch(/sid=/);
  });
});