import { config } from 'dotenv';
config();

// Ensure test database URL is set
if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL must be set in test environment');
}

// Import mock Prisma client for tests
import { prisma } from './mocks/prisma';

// Setup before tests
beforeAll(async () => {
  console.log('Test setup complete');
});

// Clean up after tests
afterAll(async () => {
  console.log('Test cleanup complete');
});

export { prisma };