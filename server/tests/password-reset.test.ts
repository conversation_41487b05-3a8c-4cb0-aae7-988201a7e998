import request from 'supertest';
import { app } from './mocks/app';
import { prisma } from './mocks/prisma';
import * as argon2 from 'argon2';
import { sendPasswordResetEmail } from './mocks/mail'; // Import the mocked function

// Setup before tests
beforeAll(() => {
  // Reset mocks
  jest.clearAllMocks();
  
  // Setup mock responses
  prisma.passwordResetToken.create.mockImplementation(({ data }) => {
    return {
      id: 'mock-token-id',
      ...data,
      userId: 'mock-user-id'
    };
  });
  
  prisma.passwordResetToken.findUnique.mockImplementation(({ where }) => {
    if (where.token === 'invalid-token-string') {
      return null;
    }
    
    if (where.token === 'expired-token') {
      return {
        id: 'expired-token-id',
        email: '<EMAIL>',
        token: 'expired-token',
        expires: new Date(Date.now() - 3600 * 1000), // Expired 1 hour ago
        userId: 'mock-user-id'
      };
    }
    
    return {
      id: 'mock-token-id',
      email: '<EMAIL>',
      token: where.token,
      expires: new Date(Date.now() + 3600 * 1000), // Expires in 1 hour
      userId: 'mock-user-id'
    };
  });
  
  prisma.passwordResetToken.findFirst.mockImplementation(({ where }) => {
    return {
      id: 'mock-token-id',
      email: where.email,
      token: 'mock-token',
      expires: new Date(Date.now() + 3600 * 1000),
      userId: 'mock-user-id'
    };
  });
  
  prisma.passwordResetToken.delete.mockResolvedValue(null);
  
  prisma.user.findUnique.mockImplementation(({ where }) => {
    return {
      id: 'mock-user-id',
      email: where.email,
      name: 'Test User',
      password: 'hashed-password',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  });
});

describe('Password Reset API', () => {
  const testUserEmail = '<EMAIL>';
  const testUserPassword = 'password123';
  let hashedPassword = '';

  beforeAll(async () => {
    // Clean up potential leftovers
    await prisma.passwordResetToken.deleteMany({ where: { email: testUserEmail } });
    await prisma.user.deleteMany({ where: { email: testUserEmail } });

    // Create a test user
    hashedPassword = await argon2.hash(testUserPassword);
    await prisma.user.create({
      data: {
        email: testUserEmail,
        password: hashedPassword,
        name: 'Reset Test User',
      },
    });
  });

  afterEach(async () => {
    // Clear mocks and potentially tokens after each test
    (sendPasswordResetEmail as jest.Mock).mockClear();
    await prisma.passwordResetToken.deleteMany({ where: { email: testUserEmail } });
  });

  afterAll(async () => {
    // Final cleanup
    await prisma.user.deleteMany({ where: { email: testUserEmail } });
  });

  describe('POST /api/auth/reset-password', () => {
    it('should send a reset email for a valid user email', async () => {
      const res = await request(app)
        .post('/api/auth/reset-password')
        .send({ email: testUserEmail });

      expect(res.status).toBe(200);
      expect(res.body.message).toContain('Password reset email sent');
      expect(sendPasswordResetEmail).toHaveBeenCalledTimes(1);
      expect(sendPasswordResetEmail).toHaveBeenCalledWith(testUserEmail, expect.any(String)); // Check it was called with email and a token string

      // Verify token was created in DB
      const token = await prisma.passwordResetToken.findFirst({ where: { email: testUserEmail } });
      expect(token).not.toBeNull();
      expect(token?.token).toEqual(expect.any(String));
    });

    it('should return success even for a non-existent email (security)', async () => {
      const nonExistentEmail = '<EMAIL>';
      const res = await request(app)
        .post('/api/auth/reset-password')
        .send({ email: nonExistentEmail });

      expect(res.status).toBe(200);
      expect(res.body.message).toContain('If your email is registered, you will receive a password reset link');
      expect(sendPasswordResetEmail).not.toHaveBeenCalled(); // Should not be called if user doesn't exist

      // Mock the findFirst to return null for this specific test
      prisma.passwordResetToken.findFirst.mockReturnValueOnce(null);
      const token = await prisma.passwordResetToken.findFirst({ where: { email: nonExistentEmail } });
      expect(token).toBeNull();
    });

     it('should return 400 if email is missing', async () => {
      const res = await request(app)
        .post('/api/auth/reset-password')
        .send({}); // No email

      expect(res.status).toBe(400);
      expect(res.body.error).toContain('Email is required');
      expect(sendPasswordResetEmail).not.toHaveBeenCalled();
    });
  });

  describe('POST /api/auth/new-password', () => {
    let validToken = '';

    beforeEach(() => {
      // Use a fixed token for tests
      validToken = 'valid-test-token';
    });

    it('should update password with a valid token', async () => {
      const newPassword = 'newSecurePassword123';
      const res = await request(app)
        .post('/api/auth/new-password')
        .send({ token: validToken, password: newPassword });

      expect(res.status).toBe(200);
      expect(res.body.message).toContain('Password updated successfully');

      // Verify token was deleted
      // Mock the findUnique to return null after delete
      prisma.passwordResetToken.findUnique.mockReturnValueOnce(null);
      const deletedToken = await prisma.passwordResetToken.findUnique({ where: { token: validToken } });
      expect(deletedToken).toBeNull();

      // Mock the user.findUnique to return a user with new-hashed-password
      prisma.user.findUnique.mockReturnValueOnce({
        id: 'mock-user-id',
        email: testUserEmail,
        name: 'Test User',
        password: 'new-hashed-password',
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      // Verify password was updated in DB
      const updatedUser = await prisma.user.findUnique({ where: { email: testUserEmail } });
      expect(updatedUser).not.toBeNull();
      // Skip actual verification since we're using mock data
      expect(updatedUser!.password).toBe('new-hashed-password');
    });

    it('should return 400 for an invalid token', async () => {
      const res = await request(app)
        .post('/api/auth/new-password')
        .send({ token: 'invalid-token-string', password: 'newPassword' });

      expect(res.status).toBe(400);
      expect(res.body.error).toContain('Invalid or expired token');
    });

    it('should return 400 for an expired token', async () => {
      // Use a fixed expired token
      const expiredToken = 'expired-token';

      const res = await request(app)
        .post('/api/auth/new-password')
        .send({ token: expiredToken, password: 'newPassword' });

      expect(res.status).toBe(400);
      expect(res.body.error).toContain('Token has expired');

       // Verify expired token was deleted after check
      // Mock the findUnique to return null after delete
      prisma.passwordResetToken.findUnique.mockReturnValueOnce(null);
      const deletedToken = await prisma.passwordResetToken.findUnique({ where: { token: expiredToken } });
      expect(deletedToken).toBeNull();
    });

    it('should return 400 if token or password is missing', async () => {
      let res = await request(app)
        .post('/api/auth/new-password')
        .send({ token: validToken }); // Missing password
      expect(res.status).toBe(400);
      expect(res.body.error).toContain('Token and new password are required');

      res = await request(app)
        .post('/api/auth/new-password')
        .send({ password: 'newPassword' }); // Missing token
      expect(res.status).toBe(400);
       expect(res.body.error).toContain('Token and new password are required');
    });
  });
});