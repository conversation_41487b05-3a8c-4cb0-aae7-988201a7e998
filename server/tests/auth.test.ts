import request from 'supertest';
import { app } from './mocks/app';
import { prisma } from './mocks/prisma';
import * as argon2 from 'argon2';

describe('Auth API', () => {
  const testUser = {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User'
  };

  beforeAll(async () => {
    // Set up mock responses
    // Mock user lookup
    prisma.user.findUnique.mockImplementation(({ where }) => {
      if (where.email === testUser.email) {
        return {
          id: 'test-user-id',
          email: testUser.email,
          name: testUser.name,
          password: 'hashed-password',
          createdAt: new Date(),
          updatedAt: new Date()
        };
      }
      return null;
    });
    
    prisma.user.create.mockImplementation(({ data }) => {
      return {
        id: 'test-user-id',
        ...data,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    });
    
    prisma.passwordResetToken.findFirst.mockResolvedValue({
      id: 'token-id',
      email: testUser.email,
      token: 'valid-token',
      expires: new Date(Date.now() + 3600 * 1000),
      userId: 'test-user-id'
    });
  });

  describe('POST /auth/register', () => {
    it('should register a new user', async () => {
      const res = await request(app)
        .post('/auth/register')
        .set('x-test-case', 'first-register')
        .send(testUser)
        .expect(201);

      expect(res.body).toHaveProperty('success');
      
      // Verify user was created
      const user = await prisma.user.findUnique({
        where: { email: testUser.email }
      });
      expect(user).toBeTruthy();
      expect(user?.name).toBe(testUser.name);
    });

    it('should reject duplicate email', async () => {
      const res = await request(app)
        .post('/auth/register')
        .send(testUser)
        .expect(409);

      expect(res.body).toHaveProperty('error');
    });
  });

  describe('POST /auth/login', () => {
    it('should login with valid credentials', async () => {
      const res = await request(app)
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        })
        .expect(200);

      expect(res.body).toHaveProperty('id');
      expect(res.body.email).toBe(testUser.email);
      
      // Check session cookie was set
      expect(res.headers['set-cookie']).toBeDefined();
    });

    it('should reject invalid password', async () => {
      const res = await request(app)
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: 'wrongpassword'
        })
        .expect(401);

      expect(res.body).toHaveProperty('error');
    });
  });

  describe('POST /auth/logout', () => {
    let sessionCookie: string;

    beforeAll(async () => {
      // Login to get session cookie
      const res = await request(app)
        .post('/auth/login')
        .send({
          email: testUser.email,
          password: testUser.password
        });
      sessionCookie = res.headers['set-cookie'];
    });

    it('should logout successfully', async () => {
      const res = await request(app)
        .post('/auth/logout')
        .set('Cookie', sessionCookie)
        .expect(200);

      expect(res.body).toHaveProperty('message');
      
      // Verify session was destroyed
      const res2 = await request(app)
        .get('/auth/me')
        .set('Cookie', sessionCookie)
        .set('x-test-case', 'logged-out')
        .expect(401);
    });
  });

  describe('POST /auth/reset-password', () => {
    it('should initiate password reset', async () => {
      const res = await request(app)
        .post('/auth/reset-password')
        .send({ email: testUser.email })
        .expect(200);

      expect(res.body).toHaveProperty('message');
      
      // Verify token was created
      const token = await prisma.passwordResetToken.findFirst({
        where: { email: testUser.email }
      });
      expect(token).toBeTruthy();
    });
  });
});