# Development stage
FROM node:18-alpine AS development
WORKDIR /app

# Install pnpm
RUN corepack enable
RUN corepack prepare pnpm@8.15.3 --activate

# Copy all dependency-related files first
COPY package.json tsconfig.json ./
COPY prisma ./prisma

# Install all dependencies (including dev dependencies)
RUN pnpm install

# Generate prisma client
RUN pnpm exec prisma generate

# Copy root package.json to get zod-to-openapi
COPY ../package.json ../

# Copy remaining source code
COPY . .

# Skip build in development stage - we'll run in dev mode
# RUN pnpm build

# Production stage
FROM node:18-alpine AS production
WORKDIR /app

# Install pnpm
RUN corepack enable
RUN corepack prepare pnpm@8.15.3 --activate

# Copy package.json and install production dependencies
COPY package.json ./
COPY --from=development /app/node_modules ./node_modules
RUN pnpm install --prod

# Copy built files and prisma
COPY --from=development /app/dist ./dist
COPY --from=development /app/prisma ./prisma

EXPOSE 3001
CMD ["pnpm", "start"]