# Plan to Fix Session Type Error in `server/src/routes/auth.ts`

## Problem

The TypeScript compiler throws the following error in `server/src/routes/auth.ts`:

```
error TS2339: Property 'userId' does not exist on type 'Session & Partial<SessionData>'.
```

This is caused by conflicting TypeScript declaration files (`.d.ts`) attempting to augment the `express-session` module's `SessionData` interface:

1.  **`server/src/types/session.d.ts`**: Defines `userId` as a required string (`userId: string;`). This matches the usage in `auth.ts`.
2.  **`server/src/types/express-session.d.ts`**: Defines `userId` as an *optional* string (`userId?: string;`). This conflicts with the usage and the other definition.

The `server/tsconfig.json` is correctly configured to include type definitions from `server/src/types`.

## Solution

1.  **Remove Conflicting Type Definition:** Delete the file `server/src/types/express-session.d.ts` to eliminate the conflict.
2.  **Ensure Module Context (Recommended):** Add `export {};` to the end of `server/src/types/session.d.ts`. This explicitly marks the file as a module, which is good practice for declaration files that augment existing modules.

## Diagram

```mermaid
graph TD
    subgraph Problem
        A[Compiler Error in auth.ts] --> B{Conflicting Session Types};
        B --> C[session.d.ts: userId: string];
        B --> D[express-session.d.ts: userId?: string];
    end

    subgraph Solution
        E[Delete express-session.d.ts] --> F{Consolidated Definition};
        C --> F;
        G[Add 'export {}' to session.d.ts] --> F;
        H[tsconfig.json confirms 'src/types' inclusion] --> I[Compiler uses session.d.ts];
        I --> J[Error Resolved];
    end

    Problem --> Solution;
```

This plan directly addresses the root cause of the TypeScript error by eliminating the conflicting type definitions and ensuring the correct one is used.