{
  "compilerOptions": {
    "target": "es2022",
    "module": "node16",
    "outDir": "./dist",
    "rootDir": ".",
    "typeRoots": [
      "./src/@types", // Add the directory containing our custom express types
      "./src/types",
      "./node_modules/@types"
    ],
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "isolatedModules": true,
    "types": ["node", "jest", "express-session"],
    "paths": {
      "@prisma/client": ["./node_modules/@prisma/client"]
    }
  },
  "include": ["src/**/*", "tests/**/*"],
  "exclude": ["node_modules"],
}