import express from 'express';
// Removed: import cookieParser from 'cookie-parser';
import { PrismaClient } from '@prisma/client';
import { env } from './config';
import { corsMiddleware } from './middleware/session'; // Removed sessionMiddleware import

const prisma = new PrismaClient();
const app = express();

// Middleware
app.use(express.json());
// Removed: app.use(cookieParser());
// Removed: app.use(sessionMiddleware);
app.use(corsMiddleware);

// Database connection middleware
app.use(async (req, res, next) => {
  try {
    await prisma.$connect();
    next();
  } catch (error) {
    console.error('Database connection error:', error);
    res.status(500).json({ error: 'Database connection failed' });
  }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

export { app, prisma };