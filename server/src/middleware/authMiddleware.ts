import { Request, Response, NextFunction } from 'express';
// Type augmentation should be picked up automatically by TS if declaration file exists
import jwt from 'jsonwebtoken';
import { logger } from '../lib/logger';
import { AuthorizationError } from '../lib/errors';

export const authenticateJWT = (req: Request, res: Response, next: NextFunction) => {
  let token: string | undefined;

  // 1. Check Authorization header (Bearer token)
  const authHeader = req.headers.authorization;
  if (authHeader?.startsWith('Bearer ')) {
    token = authHeader.split(' ')[1];
  }

  // 2. <PERSON>ie check removed - Relying solely on <PERSON><PERSON> token for API auth
  else if (!token) {
     logger.debug('No token found in Authorization header.');
  }

  if (token) {
    // Use the shared AUTH_SECRET for verification
    // Use the shared AUTH_SECRET for verification
    // Import UserJwtPayload if needed, or ensure it's globally available via declaration merging
    // Assuming UserJwtPayload is available from the declaration merging in auth.ts
    try {
      const decoded = jwt.verify(token, process.env.AUTH_SECRET!) as UserJwtPayload; // Use UserJwtPayload type
      req.jwtPayload = decoded; // Assign to req.jwtPayload
      next();
    } catch (err: any) {
        logger.warn('JWT verification failed', { error: err?.message });
        // Distinguish between different errors if needed (e.g., expired token)
        if (err instanceof jwt.TokenExpiredError) {
          return res.status(401).json({ error: 'Unauthorized: Token expired' }); // Use 401 for expired
        }
        return res.status(403).json({ error: 'Forbidden: Invalid token' }); // Use 403 for invalid signature etc.
    }
  } else {
    res.sendStatus(401);
  }
};

export const authorizeRoles = (...requiredRoles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Check req.jwtPayload and roleNames within it
    if (!req.jwtPayload ||
        !req.jwtPayload.roleNames ||
        !req.jwtPayload.roleNames.some(roleName => requiredRoles.includes(roleName))) {
      logger.warn('Authorization failed: User does not have required roles', {
        userId: req.jwtPayload?.id, // Access id from jwtPayload
        userRoles: req.jwtPayload?.roleNames,
        requiredRoles
      });
      return res.status(403).json({
        error: 'FORBIDDEN',
        message: 'Insufficient permissions'
      });
    }
    next();
  };
};