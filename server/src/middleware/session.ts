import { Request, Response, NextFunction } from 'express';
import cors from 'cors';
// Removed express-session import and sessionMiddleware export as they are no longer needed
import { env } from '../config';


export const corsMiddleware = cors({
  origin: (origin, callback) => {
    if (origin && origin.endsWith('.yourdomain.com')) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  allowedHeaders: ['Content-Type', 'Authorization', 'Cookie'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
});