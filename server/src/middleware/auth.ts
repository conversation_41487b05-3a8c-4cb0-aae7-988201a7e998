import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { env } from '../config'; // Assuming AUTH_SECRET is loaded here
import { JwtPayload } from 'jsonwebtoken'; // Import JwtPayload type

// Define the expected structure of the JWT payload based on Auth.js jwt callback
interface UserJwtPayload extends JwtPayload {
  id: string;
  email?: string;
  name?: string | null;
  picture?: string | null;
  roleNames?: string[];
  permissionNames?: string[];
  applicationPaths?: string[];
  // Add other fields if they are included in the token by Auth.js jwt callback
}

// Use declaration merging to add the jwtPayload to the Express Request interface
declare global {
  namespace Express {
    interface Request {
      jwtPayload?: UserJwtPayload;
    }
  }
}
// Removed: export interface AuthenticatedRequest extends Request { ... }

export const verifyJwt = (req: Request, res: Response, next: NextFunction) => { // Use standard Request type
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized: No token provided' });
  }

  const token = authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Unauthorized: Malformed token' });
  }

  try {
    // Verify the token using the secret from Auth.js
    // Ensure env.AUTH_SECRET is correctly loaded and matches the one used by NextAuth
    const decoded = jwt.verify(token, env.AUTH_SECRET) as UserJwtPayload; // Assert type after verification
    req.jwtPayload = decoded; // Attach decoded payload using the new property name
    next(); // Token is valid, proceed to the next middleware/route handler
  } catch (error) {
    console.error('JWT Verification Error:', error);
    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({ error: 'Unauthorized: Token expired' });
    }
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({ error: 'Unauthorized: Invalid token' });
    }
    return res.status(500).json({ error: 'Internal server error during token verification' });
  }
};