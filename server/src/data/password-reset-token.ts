import prisma from '../lib/prisma';

/**
 * Finds a password reset token by the associated email address.
 * @param email - The email address to search for.
 * @returns A promise that resolves to the token object or null if not found.
 */
export const getPasswordResetTokenByEmail = async (email: string) => {
  try {
    const passwordResetToken = await prisma.passwordResetToken.findFirst({
      where: { email },
    });
    return passwordResetToken;
  } catch {
    return null;
  }
};

/**
 * Finds a password reset token by its unique token string.
 * @param token - The token string to search for.
 * @returns A promise that resolves to the token object or null if not found.
 */
export const getPasswordResetTokenByToken = async (token: string) => {
  try {
    const passwordResetToken = await prisma.passwordResetToken.findUnique({
      where: { token },
    });
    return passwordResetToken;
  } catch {
    return null;
  }
};

/**
 * Deletes a password reset token by its ID.
 * @param id - The ID of the token to delete.
 * @returns A promise that resolves when the token is deleted.
 */
export const deletePasswordResetTokenById = async (id: string) => {
  try {
    await prisma.passwordResetToken.delete({
      where: { id },
    });
  } catch (error) {
    console.error("Error deleting password reset token:", error);
    // Decide if you want to throw or handle differently
  }
};