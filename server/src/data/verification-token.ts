import prisma from "../lib/prisma"; // Use server's prisma instance

export const getVerificationTokenByToken = async (token: string) => {
  try {
    const verificationToken = await prisma.verificationToken.findUnique({
      where: { token }
    });

    return verificationToken;
  } catch {
    // Log error appropriately in a real app
    console.error("Error fetching verification token by token:", token);
    return null;
  }
};

export const getVerificationTokenByEmail = async (email: string) => {
  try {
    const verificationToken = await prisma.verificationToken.findFirst({
      where: { email }
    });

    return verificationToken;
  } catch {
    // Log error appropriately in a real app
    console.error("Error fetching verification token by email:", email);
    return null;
  }
};