export class ApplicationError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public details?: Record<string, unknown>
  ) {
    super(message);
    this.name = this.constructor.name;
  }
}

export class AuthenticationError extends ApplicationError {
  constructor(message = "Authentication required", details?: Record<string, unknown>) {
    super(message, 401, details);
  }
}

export class AuthorizationError extends ApplicationError {
  constructor(message = "Permission denied", details?: Record<string, unknown>) {
    super(message, 403, details);
  }
}

export class ValidationError extends ApplicationError {
  constructor(message = "Invalid request data", details?: Record<string, unknown>) {
    super(message, 400, details);
  }
}

export class NotFoundError extends ApplicationError {
  constructor(message = "Resource not found", details?: Record<string, unknown>) {
    super(message, 404, details);
  }
}

export class RateLimitError extends ApplicationError {
  constructor(message = "Too many requests", details?: Record<string, unknown>) {
    super(message, 429, details);
  }
}