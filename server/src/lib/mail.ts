import { Resend } from "resend";
import { env } from '../config'; // Assuming config loads .env

// Initialize Resend client
// Falls back to null if API key is missing, allowing console logging for dev
const resend = env.RESEND_API_KEY
  ? new Resend(env.RESEND_API_KEY)
  : null;

// Base URL for constructing links (should point to the client application)
const clientBaseUrl = env.CLIENT_URL || "http://localhost:3000"; // Fallback for safety

// Default sender email address (should be a verified domain in Resend)
const fromEmail = env.EMAIL_FROM || "<EMAIL>";

/**
 * Sends a verification email to the user.
 * @param to - The recipient's email address.
 * @param token - The verification token.
 */
export const sendVerificationEmail = async (to: string, token: string) => {
  const confirmLink = `${clientBaseUrl}/auth/email-verification?token=${token}`; // Adjust path if needed

  if (resend) {
    try {
      const { data, error } = await resend.emails.send({
        from: fromEmail,
        to: to,
        subject: "Verify your email address",
        html: `<p>Click <a href="${confirmLink}">here</a> to verify your email.</p>`
      });

      if (error) {
        console.error(`Error sending verification email via Resend to ${to}:`, error);
        // Potentially throw error or handle failure
        return;
      }
      console.log(`Verification email sent successfully to ${to}. ID: ${data?.id}`);

    } catch (error) {
      console.error(`Exception sending verification email via Resend to ${to}:`, error);
      // Potentially throw error or handle failure
    }
  } else {
    // Log email details to console for development
    console.log("\n--- 📧 Verification Email (DEV LOG) ---");
    console.log("To:", to);
    console.log("Subject: Verify your email address");
    console.log("Verification Link:", confirmLink);
    // console.log("Token:", token); // Avoid logging tokens generally
    console.log("----------------------------------------");
    console.log("No RESEND_API_KEY found. Email not actually sent.");
    console.log("----------------------------------------\n");
  }
};

/**
 * Sends a password reset email to the user.
 * @param to - The recipient's email address.
 * @param token - The password reset token.
 */
export const sendPasswordResetEmail = async (to: string, token: string) => {
  const resetLink = `${clientBaseUrl}/auth/reset-password?token=${token}`; // Adjust path if needed

  if (resend) {
     try {
      const { data, error } = await resend.emails.send({
        from: fromEmail,
        to: to,
        subject: "Reset your password",
        html: `<p>Click <a href="${resetLink}">here</a> to reset your password.</p>`
      });

       if (error) {
        console.error(`Error sending password reset email via Resend to ${to}:`, error);
        // Potentially throw error or handle failure
        return;
      }
      console.log(`Password reset email sent successfully to ${to}. ID: ${data?.id}`);

    } catch (error) {
      console.error(`Exception sending password reset email via Resend to ${to}:`, error);
      // Potentially throw error or handle failure
    }
  } else {
    // Log email details to console for development
    console.log("\n--- 📧 Password Reset Email (DEV LOG) ---");
    console.log("To:", to);
    console.log("Subject: Reset your password");
    console.log("Reset Link:", resetLink);
    // console.log("Token:", token); // Avoid logging tokens generally
    console.log("----------------------------------------");
    console.log("No RESEND_API_KEY found. Email not actually sent.");
    console.log("----------------------------------------\n");
  }
};

// Add other email functions (like sendTwoFactorTokenEmail) here if needed