import { Prisma, PrismaClient } from '@prisma/client';
import { logger } from './logger';

const prisma = new PrismaClient({
  log: [
    { level: 'warn', emit: 'event' },
    { level: 'info', emit: 'event' },
    { level: 'error', emit: 'event' },
  ],
});

prisma.$on('warn', (e) => logger.warn(e));
prisma.$on('info', (e) => logger.info(e));
prisma.$on('error', (e) => logger.error(e));

export const db = prisma;
export type User = Prisma.UserGetPayload<{
  include: {
    userRoles: true
  }
}>;