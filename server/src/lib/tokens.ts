import { v4 as uuidv4 } from "uuid";
import prisma from './prisma'; // Use server's prisma instance
import { getPasswordResetTokenByEmail } from '../data/password-reset-token'; // Assuming this exists server-side
import { getVerificationTokenByEmail } from '../data/verification-token'; // Import new function

/**
 * Generate a password reset token for a user on the server.
 * @param email - The user's email.
 * @returns The generated token object or null if user doesn't exist.
 */
export async function generatePasswordResetToken(email: string) {
  const token = uuidv4();
  const expires = new Date(Date.now() + 3600 * 1000); // 1 hour expiry

  // Verify user exists first (optional but good practice)
  const existingUser = await prisma.user.findUnique({
    where: { email },
  });

  if (!existingUser) {
    // Or throw an error, depending on how you want to handle this in routes
    return null;
  }

  // Delete any existing token for this email
  const existingToken = await getPasswordResetTokenByEmail(email);
  if (existingToken) {
    await prisma.passwordResetToken.delete({
      where: { id: existingToken.id },
    });
  }

  // Create the new token
  const passwordResetToken = await prisma.passwordResetToken.create({
    data: {
      email,
      token,
      expires,
      userId: existingUser.id // Add the userId link
    },
  });

  return passwordResetToken;
}

/**
 * Generate a verification token for a user on the server.
 * @param email - The user's email.
 * @returns The generated token object.
 */
export async function generateVerificationToken(email: string) {
  const token = uuidv4();
  const expires = new Date(new Date().getTime() + 3600 * 1000); // 1 hour expiry

  // Delete any existing token for this email
  const existingToken = await getVerificationTokenByEmail(email);
  if (existingToken) {
    await prisma.verificationToken.delete({
      where: { id: existingToken.id },
    });
  }

  // Create the new token
  const verificationToken = await prisma.verificationToken.create({
    data: {
      email,
      token,
      expires,
    },
  });

  return verificationToken;
}