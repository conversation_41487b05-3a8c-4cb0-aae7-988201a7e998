import { NextResponse } from "next/server";
import { createDocument } from "zod-to-openapi";
import { z } from "zod";

export async function GET() {
  const testSchema = z.object({
    id: z.string(),
    name: z.string(),
  });

  const document = createDocument({
    openapi: "3.1.0",
    info: {
      version: "1.0.0",
      title: "API",
    },
    paths: {},
    components: {
        schemas:{
            Test: testSchema,
        }
    }
  });

  return NextResponse.json(document);
}