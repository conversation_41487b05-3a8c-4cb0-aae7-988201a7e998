import express from 'express';
import cors from 'cors';
import cookieParser from 'cookie-parser';
import session from 'express-session';
import { PrismaClient } from '@prisma/client';
import { json } from 'body-parser';
import { authRouter } from './routes/auth';
import { apiRouter } from './routes/api';
import { authenticateJWT } from './middleware/authMiddleware'; // Import the JWT middleware
import { errorHandler } from './middleware/errorHandler';

const prisma = new PrismaClient();
const app = express();

// Middleware
app.use(json());
app.use(cookieParser());
app.use(cors({
  origin: process.env.CLIENT_URL,
  credentials: true
}));


// Session middleware
app.use(session({
  secret: process.env.SESSION_SECRET || 'your_very_secret_key', // Use an environment variable!
  resave: false,
  saveUninitialized: false, // Don't save sessions until something is stored
  cookie: {
    secure: process.env.NODE_ENV === 'production', // Use secure cookies in production
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // Example: 24 hours
  }
}));

// Routes
app.use('/api/auth', authRouter);
// Apply JWT authentication middleware ONLY to the general API routes
app.use('/api', authenticateJWT, apiRouter);

// Error handling
app.use(errorHandler);

const PORT = process.env.PORT || 3001;

const server = app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  server.close(async () => {
    await prisma.$disconnect();
    process.exit(0);
  });
});