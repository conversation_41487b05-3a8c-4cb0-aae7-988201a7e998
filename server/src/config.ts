import dotenv from 'dotenv';

dotenv.config();

export const env = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  PORT: parseInt(process.env.PORT || '3001'),
  DATABASE_URL: process.env.DATABASE_URL || '',
  CLIENT_URL: process.env.CLIENT_URL || 'http://localhost:3000',
  SESSION_SECRET: process.env.SESSION_SECRET || 'default-secret',
  SESSION_COOKIE_NAME: process.env.SESSION_COOKIE_NAME || 'sid',
  SESSION_COOKIE_DOMAIN: process.env.SESSION_COOKIE_DOMAIN || 'localhost',
  SESSION_COOKIE_SECURE: process.env.NODE_ENV === 'production',
  SESSION_COOKIE_HTTP_ONLY: true,
  SESSION_COOKIE_SAME_SITE: 'lax' as const,
  SESSION_COOKIE_MAX_AGE: 1000 * 60 * 60 * 24 * 7, // 1 week

  // Auth.js JWT Secret (MUST match client-side AUTH_SECRET)
  AUTH_SECRET: process.env.AUTH_SECRET || '', // Load from environment

  // Email configuration (using Resend)
  RESEND_API_KEY: process.env.RESEND_API_KEY || undefined, // Optional API key for Resend
  EMAIL_FROM: process.env.EMAIL_FROM || 'noreply@localhost' // Default sender address
};

// Validate essential environment variables
if (!env.DATABASE_URL) {
  console.error("FATAL ERROR: DATABASE_URL is not defined in the environment variables.");
  process.exit(1);
}

if (!env.AUTH_SECRET) {
  console.error("FATAL ERROR: AUTH_SECRET is not defined in the environment variables. This must match the client's Auth.js secret.");
  process.exit(1);
}