import express, { Request } from 'express';
import { Prisma } from '@prisma/client';
import { db } from '../lib/db';
import { logger } from '../lib/logger';
import { ApplicationError, AuthorizationError, NotFoundError, ValidationError } from '../lib/errors';
import { User } from '../lib/db';

const router = express.Router();

// Cache setup
const userCache = new Map<string, { data: any, timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Cache cleanup
setInterval(() => {
    const now = Date.now();
    for (const [key, value] of userCache.entries()) {
        if (now - value.timestamp > CACHE_TTL) {
            userCache.delete(key);
        }
    }
}, CACHE_TTL);

// Helper functions
const validateAccess = (requestingUser: { id: string; role: string }, targetUserId: string) => {
    if (requestingUser.role === 'admin' || requestingUser.role === 'SYSTEM') return true;
    return requestingUser.id === targetUserId;
};


// New helper function for authorization
const checkAuthorization = (requestingUser: { id: string; email?: string; roleNames?: string[] } | undefined, targetUserId: string) => {
    if (!requestingUser) {
        throw new AuthorizationError('Authentication required');
    }

    // Determine effective role for authorization
    let effectiveRole = 'user'; // Default role
    // Check roleNames array for admin/system role
    const isAdminOrSystem = requestingUser.roleNames?.includes('admin') || requestingUser.roleNames?.includes('SYSTEM');

    if (isAdminOrSystem) {
        effectiveRole = requestingUser.roleNames?.find(r => r === 'admin' || r === 'SYSTEM') || 'user';
    }

    const authUser = { id: requestingUser.id, role: effectiveRole };

    if (!validateAccess(authUser, targetUserId)) {
        throw new AuthorizationError('Unauthorized access');
    }
};

const sanitizeUserData = (user: any, includeSensitiveData: boolean) => {
    return {
        ...user,
        password: includeSensitiveData ? user.password : undefined,
    };
};

// Get user by email
router.get('/by-email', async (req, res) => {
    const { email, includeLoginMethods, includeSensitiveData } = req.query;
    const requestingUser = req.jwtPayload;

    try {
        // Check cache
        const cached = userCache.get(email as string);
        if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
            return res.json(sanitizeUserData(cached.data, includeSensitiveData === 'true'));
        }

        const user = await db.user.findUnique({
            where: { email: email as string },
            include: { loginMethods: includeLoginMethods === 'true' }
        });

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        checkAuthorization(requestingUser, user.id); // Use the new helper

        // Cache the result
        userCache.set(email as string, {
            data: user,
            timestamp: Date.now()
        });

        res.json(sanitizeUserData(user, includeSensitiveData === 'true'));
    } catch (error) {
        logger.error('Error fetching user by email:', error);
        if (error instanceof AuthorizationError) {
            return res.status(403).json({ message: error.message });
        }
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Get user by ID
router.get('/:id', async (req, res) => {
    const { id } = req.params;
    const { includeLoginMethods, includeSensitiveData } = req.query;
    const requestingUser = req.jwtPayload;

    try {
        // Check cache
        const cached = userCache.get(id);
        if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
            return res.json(sanitizeUserData(cached.data, includeSensitiveData === 'true'));
        }

        const user = await db.user.findUnique({
            where: { id },
            include: { loginMethods: includeLoginMethods === 'true' }
        });

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        checkAuthorization(requestingUser, user.id); // Use the new helper

        // Cache the result
        userCache.set(id, {
            data: user,
            timestamp: Date.now()
        });

        res.json(sanitizeUserData(user, includeSensitiveData === 'true'));
    } catch (error) {
        logger.error('Error fetching user by ID:', error);
        if (error instanceof AuthorizationError) {
            return res.status(403).json({ message: error.message });
        }
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Update user
router.put('/:id', async (req, res) => {
    const { id } = req.params;
    const data = req.body;
    const requestingUser = req.jwtPayload;

    try {
        checkAuthorization(requestingUser, id); // Use the new helper

        const updatedUser = await db.user.update({
            where: { id },
            data,
            include: { loginMethods: true }
        });

        // Update cache
        userCache.set(id, {
            data: updatedUser,
            timestamp: Date.now()
        });

        res.json(sanitizeUserData(updatedUser, false));
    } catch (error) {
        logger.error('Error updating user:', error);
        if (error instanceof AuthorizationError) {
            return res.status(403).json({ message: error.message });
        }
        res.status(500).json({ message: 'Internal server error' });
    }
});

// Delete user
router.delete('/:id', async (req, res) => {
    const { id } = req.params;
    const requestingUser = req.jwtPayload;

    try {
        checkAuthorization(requestingUser, id); // Use the new helper

        await db.user.delete({ where: { id } });
        userCache.delete(id);
        res.status(204).end();
    } catch (error) {
        logger.error('Error deleting user:', error);
        if (error instanceof AuthorizationError) {
            return res.status(403).json({ message: error.message });
        }
        res.status(500).json({ message: 'Internal server error' });
    }
});

export default router;