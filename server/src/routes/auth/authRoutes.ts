import express from 'express';
import { logger } from '../../middleware/logging';
import { 
  registerUser,
  resetPassword,
  updatePassword,
  loginUser,
  logoutUser,
  resendVerificationEmail,
  getVerificationToken,
  verifyTwoFactorToken,
  getUserInfo,
  updateUserInfo,
  deleteUserAccount
} from '../../controllers/authController';

const router = express.Router();

// Request logging middleware
router.use((req, res, next) => {
  logger.info(`Auth route accessed: ${req.method} ${req.path}`, {
    body: req.body,
    params: req.params,
    query: req.query
  });
  next();
});

// Authentication routes
router.post('/logout', logoutUser);
router.post('/login', loginUser);
router.post('/register', registerUser);
router.post('/reset-password', resetPassword);
router.post('/new-password', updatePassword);
router.post('/resend-verification', resendVerificationEmail);

// Token verification routes
router.get('/verification-token/:token', getVerificationToken);
router.post('/verify-2fa', verifyTwoFactorToken);

// User management routes
router.get('/user/:id', getUserInfo);
router.put('/user/:id', updateUserInfo);
router.delete('/user/:id', deleteUserAccount);

// Error handling middleware
router.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error('Auth route error:', { 
    error: err.message,
    stack: err.stack,
    path: req.path,
    method: req.method
  });
  res.status(500).json({ error: 'Internal server error' });
});

export default router;