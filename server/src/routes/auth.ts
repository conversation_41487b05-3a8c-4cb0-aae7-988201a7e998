import { Router, Request, Response } from 'express';
import * as argon2 from 'argon2';
// Removed: import session from 'express-session';
// Removed: import type { Session, SessionData } from 'express-session';
import { z } from 'zod'; // Import Zod
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { getVerificationTokenByToken } from '../data/verification-token';
import prisma from '../lib/prisma';
// Remove Yup validators import if switching fully to Zod for this route
// import { validateLogin, validateRegister } from '../validators/auth';
import {
  generatePasswordResetToken,
  generateVerificationToken // Add verification token generator
} from '../lib/tokens'; // Assuming tokens lib exists on server
import {
  sendPasswordResetEmail,
  sendVerificationEmail // Add verification email sender
} from '../lib/mail'; // Assuming mail lib exists on server
import { getPasswordResetTokenByToken, deletePasswordResetTokenById } from '../data/password-reset-token'; // Keep for new-password route

const router = Router();
// const JWT_SECRET = process.env.JWT_SECRET || 'secret'; // Remove JWT secret

// --- Zod Schema for Registration ---
const RegisterSchema = z.object({
  email: z.string().email({ message: "Valid email is required" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
  name: z.string().min(1, { message: "Name is required" }),
});
// --- Zod Schema for Login ---
const LoginSchema = z.object({
  email: z.string().email({ message: "Valid email is required" }),
  password: z.string().min(1, { message: "Password is required" }), // Min 1, actual length check isn't needed here
});

router.post('/login', async (req: Request, res: Response) => { // Removed session types from req
  try {
    // 1. Validate input
    const validatedFields = LoginSchema.safeParse(req.body);
    if (!validatedFields.success) {
       const errors = validatedFields.error.flatten().fieldErrors;
       return res.status(400).json({ error: "Invalid fields", details: errors });
    }
    const { email, password } = validatedFields.data;

    // 2. Find user
    const user = await prisma.user.findUnique({
      where: { email: email }
    });
    if (!user) {
       console.log(`Login attempt failed: User not found for email ${email}`);
       return res.status(401).json({ error: 'Invalid credentials' }); // Use 401 Unauthorized
    }

    // 3. Verify password
    // Add this check
    if (!user.password) {
       console.log(`Login attempt failed: User ${email} has no password set.`);
       return res.status(401).json({ error: 'Invalid credentials' });
    }

    const validPassword = await argon2.verify(user.password, password);
    if (!validPassword) {
       console.log(`Login attempt failed: Invalid password for email ${email}`);
       return res.status(401).json({ error: 'Invalid credentials' }); // Use 401 Unauthorized
    }

    // --- Session Management Removed ---
    // No server-side session is created. Auth.js on the client will handle session creation via JWT.

    console.log(`Login successful: Credentials verified for user ${user.id}.`);

    // Send back user info (excluding password) needed by Auth.js authorize callback
    // Ensure this matches the structure expected by client/auth.config.ts
    const responseUser = {
      id: user.id,
      email: user.email,
      name: user.name,
      // Add any other fields expected by the Auth.js authorize callback's return type
      // These should ideally match the Prisma User model structure used on the client
      // Safely access potentially optional fields or provide defaults
      emailVerified: user.emailVerified ?? null,
      image: user.image ?? null,
      role: (user as any).role ?? 'user', // Use 'user' as default if role doesn't exist or is null/undefined
      status: (user as any).status ?? 'active', // Use 'active' as default
      isTwoFactorEnabled: (user as any).isTwoFactorEnabled ?? false, // Default to false
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      // Add other fields as needed by client...
    };

    res.json(responseUser);
    // --- End Session Management Removed ---

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ error: 'Login failed due to an internal error' });
  }
});

router.post('/register', async (req: Request, res: Response) => {
  try {
    // 1. Validate input using Zod
    const validatedFields = RegisterSchema.safeParse(req.body);
    if (!validatedFields.success) {
      const errors = validatedFields.error.flatten().fieldErrors;
      return res.status(400).json({ error: "Invalid fields", details: errors });
    }
    const { email, password, name } = validatedFields.data;

    // 2. Check if user already exists
    const existingUser = await prisma.user.findUnique({ where: { email } });
    if (existingUser) {
      // Consider if email verification is pending for existing user?
      // For now, simple conflict is fine.
      return res.status(409).json({ error: "Email already in use" }); // 409 Conflict
    }

    // 3. Hash password
    const hashedPassword = await argon2.hash(password, {
      type: argon2.argon2id,
      memoryCost: 19456,
      timeCost: 2,
      parallelism: 1
    });

    // 4. Create user
    const newUser = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        // emailVerified: null, // Explicitly set if needed by schema/logic
      },
    });

    // 5. Generate verification token & send email
    // Ensure generateVerificationToken and sendVerificationEmail are implemented server-side
    const verificationToken = await generateVerificationToken(email);
    if (!verificationToken) {
       // Handle error during token generation if necessary
       console.error(`Failed to generate verification token for ${email}`);
       // Maybe proceed without verification or return specific error?
       // For now, assume it works or throws internally.
    } else {
       await sendVerificationEmail(email, verificationToken.token);
    }


    // 6. Return success (no user data or JWT needed here)
    res.status(201).json({ success: "User registered. Confirmation email sent." });

  } catch (error) {
    // Log the actual error for debugging
    console.error('Registration error:', error);

    // Handle potential Prisma errors if needed (e.g., DB connection issues)
    if (error instanceof PrismaClientKnownRequestError) {
       // Log Prisma specific details
       console.error('Prisma Error Code:', error.code);
    }

    // Generic error response
    res.status(500).json({ error: 'Registration failed due to an internal error.' });
  }
});

// Removed: router.post('/logout', ...)
// Logout is now handled client-side by Auth.js (clearing the JWT cookie)

// --- Password Reset ---

router.post('/reset-password', async (req, res) => {
  const { email } = req.body;

  if (!email) {
    return res.status(400).json({ error: 'Email is required' });
  }

  try {
    // 1. Check if user exists (generatePasswordResetToken does this)
    // 2. Generate password reset token
    const passwordResetToken = await generatePasswordResetToken(email);

    if (!passwordResetToken) {
      // Handle case where user doesn't exist without revealing it explicitly for security?
      // Or rely on generatePasswordResetToken throwing an error if needed.
      // For now, assume success even if user doesn't exist to prevent email enumeration.
      return res.json({ message: 'If your email is registered, you will receive a password reset link.' });
    }

    // 3. Send password reset email
    await sendPasswordResetEmail(email, passwordResetToken.token);

    res.json({ message: 'Password reset email sent successfully.' });

  } catch (error) {
    console.error('Error initiating password reset:', error);
    res.status(500).json({ error: 'Failed to initiate password reset' });
  }
});

router.post('/new-password', async (req, res) => {
  const { token, password } = req.body;

  if (!token || !password) {
    return res.status(400).json({ error: 'Token and new password are required' });
  }
  
  // Password strength validation
  const passwordSchema = z.string()
    .min(8, { message: "Password must be at least 8 characters long" })
    .regex(/[A-Z]/, { message: "Password must contain at least one uppercase letter" })
    .regex(/[a-z]/, { message: "Password must contain at least one lowercase letter" })
    .regex(/[0-9]/, { message: "Password must contain at least one number" })
    .regex(/[^A-Za-z0-9]/, { message: "Password must contain at least one special character" });
  
  const passwordResult = passwordSchema.safeParse(password);
  if (!passwordResult.success) {
    const errors = passwordResult.error.flatten().formErrors;
    return res.status(400).json({ error: 'Password is not strong enough', details: errors });
  }

  try {
    // 1. Validate token (exists, not expired)
    const existingToken = await getPasswordResetTokenByToken(token);

    if (!existingToken) {
      return res.status(400).json({ error: 'Invalid or expired token' });
    }

    const hasExpired = new Date(existingToken.expires) < new Date();
    if (hasExpired) {
      // Optionally delete expired token here
      await deletePasswordResetTokenById(existingToken.id);
      return res.status(400).json({ error: 'Token has expired' });
    }

    // Use a transaction to ensure atomicity
    // Infer the transaction client type from the prisma instance
    type PrismaTransactionClient = Omit<typeof prisma, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>;

    await prisma.$transaction(async (tx: PrismaTransactionClient) => {
      // 2. Find user by token email (within transaction)
      const existingUser = await tx.user.findUnique({
        where: { email: existingToken.email },
      });

      if (!existingUser) {
        // This will rollback the transaction
        throw new Error('User not found for this token');
      }
      console.log(`Found user with ID: ${existingUser.id} for email: ${existingUser.email}`); // DEBUG LOG

      // 3. Hash new password
      const hashedPassword = await argon2.hash(password, {
        type: argon2.argon2id,
        memoryCost: 19456,
        timeCost: 2,
        parallelism: 1
      });

      // 4. Update user password in DB (within transaction)
      console.log(`Attempting to update user with ID: ${existingUser.id}`); // DEBUG LOG
      // No need for the extra diagnostic check within a transaction
      await tx.user.update({
        where: { id: existingUser.id },
        data: { password: hashedPassword },
      });
      console.log(`Successfully updated user with ID: ${existingUser.id}`); // DEBUG LOG


      // 5. Delete used token from DB (within transaction)
      // Use the main prisma client or the transaction client?
      // Using tx ensures it's part of the same transaction.
      // We already have the ID, so no need for deletePasswordResetTokenById helper here.
       await tx.passwordResetToken.delete({
         where: { id: existingToken.id },
       });
    }); // End transaction

    // If transaction succeeds:
    res.json({ message: 'Password updated successfully' });

  } catch (error: any) {
    console.error('Error setting new password:', error);
    // Check if it's the specific error we threw in the transaction
    if (error.message === 'User not found for this token') {
       res.status(400).json({ error: 'User not found for this token' });
    } else {
       res.status(500).json({ error: 'Failed to update password' });
    }
  }
});

// --- Email Verification ---

// Verify Email Endpoint
const VerifyEmailSchema = z.object({
  token: z.string().min(1, { message: "Token is required" }),
});

router.post('/verify-email', async (req: Request, res: Response) => {
  try {
    const validatedFields = VerifyEmailSchema.safeParse(req.body);
    if (!validatedFields.success) {
      return res.status(400).json({ error: "Invalid token format" });
    }
    const { token } = validatedFields.data;

    // 1. Find the verification token
    const existingToken = await getVerificationTokenByToken(token);
    if (!existingToken) {
      return res.status(400).json({ error: "Invalid or expired token" });
    }

    // 2. Check if token is expired
    const hasExpired = new Date(existingToken.expires) < new Date();
    if (hasExpired) {
      // Delete expired token
      await prisma.verificationToken.delete({
        where: { id: existingToken.id },
      });
      return res.status(400).json({ error: "Token has expired" });
    }

    // 3. Find the user by email
    const user = await prisma.user.findUnique({
      where: { email: existingToken.email },
    });

    if (!user) {
      return res.status(400).json({ error: "User not found" });
    }

    // 4. Update user as verified
    // Note: We've added emailVerified field to the schema, but in case
    // the migration hasn't been run yet, we'll handle it gracefully
    try {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          // @ts-ignore - This field might not exist in the schema yet
          emailVerified: new Date(),
        },
      });
    } catch (updateError) {
      console.warn('Could not update emailVerified field:', updateError);
      // Continue with the process even if this update fails
    }

    // 5. Delete the verification token
    await prisma.verificationToken.delete({
      where: { id: existingToken.id },
    });

    // 6. Return success
    res.json({ success: "Email verified successfully" });
  } catch (error) {
    console.error('Error verifying email:', error);
    res.status(500).json({ error: 'Failed to verify email' });
  }
});

// --- Resend Verification Email Endpoint ---
const ResendVerificationSchema = z.object({
  email: z.string().email({ message: "Valid email is required" }),
});

router.post('/resend-verification', async (req: Request, res: Response) => {
  try {
    const validatedFields = ResendVerificationSchema.safeParse(req.body);
    if (!validatedFields.success) {
      return res.status(400).json({ error: "Invalid email format" });
    }
    const { email } = validatedFields.data;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({ where: { email } });
    if (!existingUser) {
      // Return success even if user doesn't exist to prevent email enumeration
      return res.json({ success: "If your email is registered, a new confirmation email has been sent." });
    }

    // Check if user is already verified? (Optional - depends on requirements)
    // if (existingUser.emailVerified) {
    //   return res.json({ success: "Email is already verified." });
    // }

    // Generate new token and send email
    const verificationToken = await generateVerificationToken(email);
    if (!verificationToken) {
      throw new Error("Failed to generate verification token"); // Should be caught below
    }
    await sendVerificationEmail(email, verificationToken.token);

    res.json({ success: "A new confirmation email has been sent." });

  } catch (error) {
    console.error('Error resending verification email:', error);
    res.status(500).json({ error: 'Failed to resend verification email.' });
  }
});


export const authRouter = router;