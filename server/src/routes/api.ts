import { Router, Request, Response } from 'express'; // Add Request, Response
import type { Session, SessionData } from 'express-session';
import prisma from '../lib/prisma';

const router = Router();

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// User routes
router.get('/users/me', async (req, res) => {
  try {
    // Check if user is authenticated
    if (!(req.session as Session & { userId: string }).userId) {
      return res.status(401).json({ error: 'Not authenticated' });
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: (req.session as Session & { userId: string }).userId },
      select: {
        id: true,
        email: true,
        name: true,
        // emailVerified field will be available after migration
        // emailVerified: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Health check for user service
router.get('/users/health', (req, res) => {
  res.json({ status: 'ok', service: 'users' });
});

export const apiRouter = router;