import express, { Request, Response } from 'express'; // Import Request and Response explicitly
// Removed: import cookieParser from 'cookie-parser';
import { corsMiddleware } from './middleware/session'; // Only import corsMiddleware
import { authRouter } from './routes/auth';
import { verifyJwt } from './middleware/auth'; // Removed AuthenticatedRequest import

const app = express();

app.use(corsMiddleware);
app.use(express.json());
// Removed: app.use(cookieParser()); - No longer needed if not using signed cookies elsewhere
// Removed: app.use(sessionMiddleware); - Replaced by JWT auth

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

app.use('/api/auth', authRouter); // Public auth routes

// --- Example Protected Route ---
// Apply the verifyJwt middleware to any routes that require authentication
app.get('/api/protected-data', verifyJwt, (req: Request, res: Response) => { // Use explicitly imported types
  // If execution reaches here, the JWT was valid.
  // The decoded user payload is available in req.user
  res.json({
    message: "This is protected data.",
    user: req.jwtPayload // Send back the decoded user info from the token using the correct property
  });
});
// --- End Example ---


export { app };