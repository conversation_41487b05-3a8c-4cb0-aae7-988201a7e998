import os
from datetime import datetime, timedelta
from sqlalchemy import create_engine, Column, String, DateTime, ForeignKey
from sqlalchemy.orm import sessionmaker, relationship, declarative_base
import uuid

# Database connection - use provided credentials with SQLite fallback
DEFAULT_PG_URL = "postgresql://dennis:gssw9w48p90@localhost:5432/postgres"
DATABASE_URL = os.getenv("DATABASE_URL", DEFAULT_PG_URL)
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Models matching Prisma schema
class User(Base):
    __tablename__ = "user"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, unique=True, nullable=False)
    name = Column(String)
    password = Column(String, nullable=False)
    email_verified = Column(DateTime)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    sessions = relationship("Session", back_populates="user")

class Session(Base):
    __tablename__ = "session"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(String, ForeignKey("user.id"))
    expires_at = Column(DateTime, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    user = relationship("User", back_populates="sessions")

class PasswordResetToken(Base):
    __tablename__ = "password_reset_token"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, nullable=False)
    token = Column(String, unique=True, nullable=False)
    expires = Column(DateTime, nullable=False)
    user_id = Column(String)

class VerificationToken(Base):
    __tablename__ = "verification_token"
    
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    email = Column(String, nullable=False)
    token = Column(String, unique=True, nullable=False)
    expires = Column(DateTime, nullable=False)
def create_tables():
    try:
        Base.metadata.create_all(bind=engine)
        print("Tables created successfully")
    except Exception as e:
        print(f"Error creating tables: {e}")
        print("\nTroubleshooting tips:")
        print("1. Ensure PostgreSQL is running: 'brew services start postgresql' (Mac)")
        print("2. Verify credentials in the script match your PostgreSQL setup")
        print("3. Try using SQLite by setting DATABASE_URL=sqlite:///./test.db")
    Base.metadata.create_all(bind=engine)

def create_sample_data():
    db = SessionLocal()
    
    try:
        # Create sample users
        users = [
            User(
                email="<EMAIL>",
                name="Test User 1",
                password="hashed_password_1",
                email_verified=datetime.utcnow()
            ),
            User(
                email="<EMAIL>",
                name="Test User 2",
                password="hashed_password_2"
            )
        ]
        
        db.add_all(users)
        db.commit()
        
        # Create sessions for users
        sessions = [
            Session(
                user_id=users[0].id,
                expires_at=datetime.utcnow() + timedelta(days=1)
            ),
            Session(
                user_id=users[1].id,
                expires_at=datetime.utcnow() + timedelta(days=1)
            )
        ]
        
        db.add_all(sessions)
        db.commit()
        
        # Create tokens
        tokens = [
            PasswordResetToken(
                email="<EMAIL>",
                token=str(uuid.uuid4()),
                expires=datetime.utcnow() + timedelta(hours=1),
                user_id=users[0].id
            ),
            VerificationToken(
                email="<EMAIL>",
                token=str(uuid.uuid4()),
                expires=datetime.utcnow() + timedelta(hours=1)
            )
        ]
        
        db.add_all(tokens)
        db.commit()
        
        print("Successfully created sample data")
    except Exception as e:
        db.rollback()
        print(f"Error creating sample data: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print(f"Connecting to database at: {DATABASE_URL}")
    print("Creating database tables...")
    create_tables()
    print("Creating sample data...")
    create_sample_data()
    print("\nDatabase setup complete!")