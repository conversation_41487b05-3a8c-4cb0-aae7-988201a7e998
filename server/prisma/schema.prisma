generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  /// 使用者的唯一標識符
  id                    String                 @id @default(cuid())
  /// 使用者名稱
  name                  String?
  /// 使用者的電子郵件地址，必須唯一
  email                 String                 @unique
  /// 電子郵件驗證時間戳
  emailVerified         DateTime?
  /// 使用者的頭像 URL
  image                 String?
  /// 使用者的密碼
  password              String?
  /// 使用者的狀態
  status                UserStatus             @default(pending)
  /// 是否啟用兩步驗證
  isTwoFactorEnabled    Boolean                @default(false)
  /// 登入嘗試次數
  loginAttempts         Int                    @default(0)
  /// 最後一次登入嘗試時間
  lastLoginAttempt      DateTime?
  /// 最後一次成功登入時間
  lastSuccessfulLogin   DateTime?
  /// 登入嘗試重置時間
  loginAttemptsResetAt  DateTime?
  /// 使用者的創建時間
  createdAt             DateTime               @default(now())
  /// 使用者的最後更新時間
  updatedAt             DateTime               @updatedAt
  accounts              Account[]
  auditLogs             AuditLog[]
  loginMethods          LoginMethod[]
  resetTokens           PasswordResetToken[]
  sessions              Session[]
  twoFactorConfirmation TwoFactorConfirmation?
  twoFactorTokens       TwoFactorToken[]
  userRoles             UserRole[]             // User to Role relationship
}

model Account {
  /// 使用者標識符
  userId            String
  /// 帳號類型
  type              String
  /// 帳號提供者 (例如：Google, Facebook)
  provider          String
  /// 提供者特定的帳號標識符
  providerAccountId String
  /// 刷新令牌 (可選)
  refresh_token     String?
  /// 訪問令牌
  access_token      String?
  /// 訪問令牌的過期時間
  expires_at        Int?
  /// 令牌類型
  token_type        String?
  /// 令牌的範圍
  scope             String?
  /// 帳號的 ID 令牌 (可選)
  id_token          String?
  /// 會話狀態
  session_state     String?
  /// 帳號的創建時間
  createdAt         DateTime @default(now())
  /// 帳號的最後更新時間
  updatedAt         DateTime @default(now())
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
  @@index([userId])
}

model Session {
  /// 會話令牌
  id           String   @id @default(cuid())
  /// 會話令牌
  sessionToken String   @unique
  /// 使用者標識符
  userId       String
  /// 會話過期時間
  expires      DateTime
  /// 最後活動時間
  lastActivity DateTime @default(now())
  /// 用戶代理
  userAgent    String?
  /// IP 地址
  ipAddress    String?
  /// 會話創建時間
  createdAt    DateTime @default(now())
  /// 會話最後更新時間
  updatedAt    DateTime @default(now())
  /// Device identifier
  deviceId     String?
  /// Session type (web, mobile, api)
  sessionType  String   @default("web")
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([deviceId])
}

model TwoFactorConfirmation {
  /// 兩步驗證確認的唯一標識符
  id        String   @id @default(cuid())
  /// 使用者標識符
  userId    String   @unique
  /// 創建時間
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model LoginMethod {
  /// 登入方法的唯一標識符
  id        String   @id @default(cuid())
  /// 使用者標識符
  userId    String
  /// 登入方法類型（例如："password", "google", "github"）
  method    String
  /// 登入方法的創建時間
  createdAt DateTime @default(now())
  /// 登入方法的最後更新時間
  updatedAt DateTime @updatedAt
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model VerificationToken {
  id      String   @id @default(cuid())
  email   String
  token   String   @unique
  expires DateTime

  @@unique([email, token])
}

model PasswordResetToken {
  /// Token ID
  id      String   @id @default(cuid())
  /// Email address associated with the token
  email   String
  /// Reset token value
  token   String   @unique
  /// Token expiration timestamp
  expires DateTime
  userId  String?
  user    User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([email])
}

model TwoFactorToken {
  /// 兩步驗證牌的唯一標識符
  id        String   @id @default(cuid())
  /// 使用者標識符
  userId    String
  /// 兩步驗證令牌
  token     String   @unique
  /// 令牌的過期時間
  expires   DateTime
  /// 是否已使用
  used      Boolean  @default(false)
  /// 創建時間
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, token])
  @@index([userId])
}

model AuditLog {
  /// 審計日誌的唯一標識符
  id           String   @id @default(cuid())
  /// 相關使用者的標識符（可為空，表示系統操作）
  userId       String?
  /// 審計操作類型
  action       String
  /// 操作狀態
  status       String
  /// 操作時間戳
  timestamp    DateTime @default(now())
  /// IP 地址
  ipAddress    String?
  /// 用戶代理
  userAgent    String?
  /// 目標使用者標識符（如果適用）
  targetUserId String?
  /// 資源標識符（如果適用）
  resourceId   String?
  /// 資源類型（如果適用）
  resourceType String?
  /// 舊值（如果適用）
  oldValue     String?
  /// 新值（如果適用）
  newValue     String?
  /// 操作原因
  reason       String?
  /// 額外元數據（JSON格式）
  metadata     Json?
  /// Log priority (low, medium, high, critical)
  priority     String   @default("low")
  /// Related session ID if applicable
  sessionId    String?
  user         User?    @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([action])
  @@index([timestamp])
  @@index([targetUserId])
  @@index([priority])
}

model Role {
  /// 角色的唯一標識符
  id          String         @id @default(cuid())
  /// 角色名稱
  name        String         @unique
  /// 角色描述
  description String?
  /// 創建時間
  createdAt   DateTime       @default(now())
  /// 更新時間
  updatedAt   DateTime       @updatedAt
  /// 與此角色關聯的使用者
  users       UserRole[]
  /// 與此角色關聯的權限
  permissions RolePermission[]
  /// 與此角色關聯的應用程式
  applications RoleApplication[]
}

model Permission {
  /// 權限的唯一標識符
  id          String         @id @default(cuid())
  /// 權限名稱
  name        String         @unique
  /// 權限描述
  description String?
  /// 創建時間
  createdAt   DateTime       @default(now())
  /// 更新時間
  updatedAt   DateTime       @updatedAt
  /// 與此權限關聯的角色
  roles       RolePermission[]
}

model UserRole {
  /// 使用者角色關聯的唯一標識符
  id        String   @id @default(cuid())
  /// 使用者標識符
  userId    String
  /// 角色標識符
  roleId    String
  /// 創建時間
  createdAt DateTime @default(now())
  /// 更新時間
  updatedAt DateTime @updatedAt
  /// 使用者
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  /// 角色
  role      Role     @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@index([userId])
  @@index([roleId])
}

model RolePermission {
  /// 角色權限關聯的唯一標識符
  id           String     @id @default(cuid())
  /// 角色標識符
  roleId       String
  /// 權限標識符
  permissionId String
  /// 創建時間
  createdAt    DateTime   @default(now())
  /// 更新時間
  updatedAt    DateTime   @updatedAt
  /// 角色
  role         Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  /// 權限
  permission   Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@index([roleId])
  @@index([permissionId])
}

model Application {
  /// 應用程式的唯一標識符
  id          String             @id @default(cuid())
  /// 應用程式名稱
  name        String             @unique
  /// 應用程式顯示名稱
  displayName String
  /// 應用程式描述
  description String?
  /// 是否啟用
  isActive    Boolean            @default(true)
  /// 應用程式路徑
  path        String             @unique
  /// 應用程式圖標
  icon        String?
  /// 排序順序
  order       Int                @default(0)
  /// 創建時間
  createdAt   DateTime           @default(now())
  /// 更新時間
  updatedAt   DateTime           @updatedAt
  /// 與此應用程式關聯的角色
  roles       RoleApplication[]
  /// 與此應用程式關聯的選單項目
  menuItems   MenuItem[]
}

model RoleApplication {
  /// 角色應用程式關聯的唯一標識符
  id            String      @id @default(cuid())
  /// 角色標識符
  roleId        String
  /// 應用程式標識符
  applicationId String
  /// 創建時間
  createdAt     DateTime    @default(now())
  /// 更新時間
  updatedAt     DateTime    @updatedAt
  /// 角色
  role          Role        @relation(fields: [roleId], references: [id], onDelete: Cascade)
  /// 應用程式
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@unique([roleId, applicationId])
  @@index([roleId])
  @@index([applicationId])
}

model MenuItem {
  /// 選單項目的唯一標識符
  id            String      @id @default(cuid())
  /// 選單項目名稱
  name          String
  /// 顯示名稱
  displayName   String
  /// 選單項目路徑
  path          String
  /// 選單項目圖標
  icon          String?
  /// 父選單項目標識符
  parentId      String?
  /// 應用程式標識符
  applicationId String
  /// 排序順序
  order         Int         @default(0)
  /// 是否顯示
  isVisible     Boolean     @default(true)
  /// 創建時間
  createdAt     DateTime    @default(now())
  /// 更新時間
  updatedAt     DateTime    @updatedAt
  /// 應用程式
  application   Application @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  /// 父選單項目
  parent        MenuItem?   @relation("MenuItemToMenuItem", fields: [parentId], references: [id], onDelete: SetNull)
  /// 子選單項目
  children      MenuItem[]  @relation("MenuItemToMenuItem")

  @@index([applicationId])
  @@index([parentId])
}

enum DefaultRole {
  user
  admin
}

enum UserStatus {
  pending
  active
  suspended
  banned
  deleted
  inactive
}
