# Server Setup Guide

## Prerequisites
- Node.js 18+
- PostgreSQL 17+
- Yarn 1.22+

## Installation
1. Install dependencies:
```bash
yarn install
```

2. Generate Prisma client:
```bash
yarn prisma:generate
```

3. Run database migrations:
```bash
yarn prisma:migrate
```

4. Seed the database (optional):
```bash
yarn prisma:seed
```

## Development
Start the development server:
```bash
yarn dev
```

## Testing
Run tests:
```bash
yarn test
```

## Environment Variables
See `.env.example` for required environment variables.