# Dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# Testing
/coverage
/.nyc_output
/cypress/screenshots
/cypress/videos

# Next.js
.next/
.next-build/
.next/cache/
.next/standalone/
.next/server/
.next/trace
.next/types
.next-env.d.ts
.next-build-id
.nx/
out/

# Production
/build
/dist

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Local env files
.env
.env*.local
.env.development
.env.test
.env.production

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# PWA
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Cache directories
.npm
.eslintcache
.stylelintcache
.prettiercache
.rollup.cache
tsconfig.tsbuildinfo

# Prisma
/prisma/*.db
/prisma/migrations/*_init
prisma/dev.db
prisma/dev.db-journal
*.env

# Build output
dist
dist-ssr
*.local
.output

# Misc
*.bak
*.swp
*.swo
.DS_Store
.thumbs.db
.tmp
.temp
.cache

# shadcn/ui
components.json

# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions
.pnp.*

# Next.js build outputs
.next
.next-build

# Node modules
node_modules

# Client specific directories
client/.next-build/
client/.next/
client/node_modules/
client/sampleApp/.next/
client/sampleApp/node_modules/
server/node_modules/
