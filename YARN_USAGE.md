# Using Yarn in This Project

This guide explains how to use yarn as your package manager in this project, even though there are parent directory configuration constraints.

## The Solution: Using `./yarnw`

We've created a special wrapper script called `yarnw` that allows you to use yarn commands regardless of parent directory settings.

## Basic Commands

Instead of using `yarn` directly, use `./yarnw` followed by your yarn commands:

```bash
# Install dependencies
./yarnw install

# Add a new dependency
./yarnw add package-name

# Add a development dependency
./yarnw add --dev package-name

# Run scripts defined in package.json
./yarnw dev
./yarnw build
./yarnw test
```

## Using in Different Directories

You can use the wrapper from any of the project directories:

```bash
# In the root directory
cd /Users/<USER>/Documents/GitHub/shadcn-template
./yarnw install

# In the client directory
cd /Users/<USER>/Documents/GitHub/shadcn-template/client
../yarnw install

# In the server directory
cd /Users/<USER>/Documents/GitHub/shadcn-template/server
../yarnw install
```

## Alias for Convenience (Optional)

For convenience, you can add an alias to your shell configuration:

```bash
# Add this to your ~/.zshrc or ~/.bash_profile
alias yarn-project="$PWD/yarnw"
```

Then use it like this:
```bash
yarn-project install
yarn-project add package-name
```

## Remember

- All `package.json` scripts have been updated to use yarn
- The project has been configured with `.npmrc` files to prefer yarn
- Lock files have been cleaned up to avoid conflicts

This approach allows you to use yarn as your package manager while working around parent directory constraints.
